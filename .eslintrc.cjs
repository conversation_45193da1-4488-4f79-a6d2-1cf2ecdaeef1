module.exports = {
    // 设置根目录，停止 ESLint 在父级目录中寻找配置文件
    root: true,
    // 指定解析器选项
    parserOptions: {
        // 使用 Babel ESLint 解析器（如果你只使用 JS）
        // "parser": "@babel/eslint-parser",
        // 如果使用 TypeScript，则使用 TypeScript 解析器
        parser: '@typescript-eslint/parser',
        // 指定 ECMAScript 版本
        ecmaVersion: 2020, // 允许解析 ES2020 语法
        sourceType: 'module', // 指定源代码类型为模块
        ecmaFeatures: {
            jsx: true, // 启用 JSX
        },
        // TypeScript 项目需要指定 tsconfig 文件路径
        project: './tsconfig.json', // 根据你的 tsconfig 文件位置调整
    },
    // 指定环境，这些环境预定义了全局变量
    env: {
        browser: true, // 浏览器全局变量
        node: true, // Node.js 全局变量
        es2020: true, // ES2020 全局变量和语法
    },
    // 扩展规则集 (extends 总是放在 plugins 之后，除非特别说明)
    extends: [
        'eslint:recommended', // ESLint 官方推荐规则
        'plugin:@typescript-eslint/recommended', // TypeScript 推荐规则
        'plugin:@typescript-eslint/recommended-type-checked', // TypeScript 强类型检查规则
        'plugin:@typescript-eslint/stylistic-type-checked', // TypeScript 风格规则
        'plugin:react/recommended', // React 推荐规则
        'plugin:react-hooks/recommended', // React Hooks 推荐规则
        'plugin:prettier/recommended', // 集成 Prettier，关闭与 Prettier 冲突的 ESLint 规则，并运行 Prettier 作为 ESLint 规则
    ],
    // 插件
    plugins: [
        '@typescript-eslint',
        'react',
        'react-hooks',
        'prettier', // 注册 Prettier 插件
    ],
    // 自定义规则
    rules: {
        // Prettier 规则，由 eslint-plugin-prettier 实现
        'prettier/prettier': [
            'error',
            {
                // 这里可以覆盖或指定 Prettier 的规则，但通常推荐在 .prettierrc.js 中统一管理
                // 比如 'singleQuote': true, 'semi': false
            },
        ],
        // React 规则
        'react/react-in-jsx-scope': 'off', // 对于 React 17+, 不需要导入 React
        'react/prop-types': 'off', // 在 TypeScript 中，Props 类型由接口定义，因此可以关闭此规则
        // TypeScript 规则
        '@typescript-eslint/no-explicit-any': 'off', // 允许使用 any，酌情开启
        '@typescript-eslint/explicit-module-boundary-types': 'off', // 不需要导出函数的显式返回类型
        '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }], // 警告未使用的变量，忽略以下划线开头的参数
        // 其他自定义规则
        'no-console': ['warn', { allow: ['warn', 'error'] }], // 警告 console.log，但允许 console.warn 和 console.error
    },
    // 设置特定文件的覆盖规则
    overrides: [
        {
            // 针对 .js 文件不使用 TypeScript 解析器
            files: ['*.js', '*.jsx'],
            parserOptions: {
                parser: '@babel/eslint-parser', // JS 文件使用 Babel 解析器
                requireConfigFile: false, // 允许没有 babel.config.js 文件
            },
            rules: {
                '@typescript-eslint/no-var-requires': 'off', // JS 文件中允许 require
            },
        },
    ],
    // 共享设置，供所有规则使用
    settings: {
        react: {
            version: 'detect', // 自动检测 React 版本
        },
    },
};