import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '../stores/useUserStore';
import { logout } from '../utils/auth';

function Home() {
  const navigate = useNavigate();
  const { clearUser } = useUserStore();

  const handleClick = () => {
    // 跳转到系统菜单页面
    navigate('/system-menu');
  };

  const handleLogout = () => {
    logout();
    clearUser();
    // 跳转到登录页面
    navigate('/login');
  };

  return (
    <div>
      <Button onClick={handleClick}>To system-menu</Button>
      <Button onClick={handleLogout}> Logout</Button>
    </div>
  );
}

export default Home;
