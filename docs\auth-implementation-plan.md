# 全局路由守卫实施计划

## 项目目标

为React应用实现一个全局路由守卫系统，拦截所有导航请求，检查用户认证状态，未认证用户重定向到登录页面并保留原始路径。

## 技术架构

### 存储方案

- **认证存储**: localStorage (持久化存储)
- **存储键名**:
  - `auth_token`: JWT认证令牌
  - `auth_user`: 用户信息(JSON字符串)
- **有效期**: token自带过期时间，前端只做格式验证

### 路由守卫机制

- **守卫位置**: 在LazyRouter组件内实现全局守卫
- **检查时机**: 每次路由变化时检查
- **重定向逻辑**: 保留原始路径在redirect查询参数中

## 实施步骤

### 阶段1: 基础类型和工具 (优先级: 高)

1. 创建认证类型定义 (`src/types/auth.ts`)
2. 创建认证工具函数 (`src/utils/auth.ts`)
3. 实现localStorage存储管理

### 阶段2: 路由守卫组件 (优先级: 高)

1. 创建AuthGuard组件 (`src/components/AuthGuard.tsx`)
2. 集成到LazyRouter中
3. 实现认证检查逻辑

### 阶段3: 登录页面 (优先级: 高)

1. 完善Login组件 (`src/pages/Login.tsx`)
2. 实现登录表单和验证
3. 处理重定向回原路径

### 阶段4: 路由配置更新 (优先级: 中)

1. 更新RouteDefinition接口支持认证配置
2. 为现有路由添加认证元数据
3. 配置公开路由

### 阶段5: 测试和优化 (优先级: 中)

1. 测试各种认证场景
2. 优化用户体验
3. 添加错误处理

## 文件结构

```
src/
├── types/
│   └── auth.ts          # 认证相关类型定义
├── utils/
│   └── auth.ts          # 认证工具函数
├── components/
│   └── AuthGuard.tsx    # 路由守卫组件
├── pages/
│   └── Login.tsx        # 登录页面(完善)
└── router/
    ├── types.ts         # 路由类型更新
    └── LazyRouter.tsx   # 集成路由守卫
```

## 核心代码预览

### 认证工具函数 (auth.ts)

```typescript
// 检查用户是否已认证
export const isAuthenticated = (): boolean => {
  const token = getToken();
  if (!token) return false;

  // 检查token是否过期
  if (token.expiresAt && Date.now() > token.expiresAt) {
    clearAuth();
    return false;
  }

  return true;
};

// 获取重定向路径
export const getRedirectPath = (): string => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('redirect') || '/';
};
```

### 路由守卫组件 (AuthGuard.tsx)

```typescript
export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation()

  if (!isAuthenticated()) {
    return <Navigate to={`/login?redirect=${encodeURIComponent(location.pathname + location.search)}`} replace />
  }

  return <>{children}</>
}
```

### 路由配置更新

```typescript
// 公开路由
{
  path: 'login',
  meta: { public: true }
}

// 需要认证的路由
{
  path: 'dashboard',
  meta: { requiresAuth: true }
}
```

## 测试场景

1. **未认证用户访问需要认证的页面**
   - 访问 `/dashboard` → 重定向到 `/login?redirect=/dashboard`
   - 登录成功后 → 重定向到 `/dashboard`

2. **已认证用户访问任何页面**
   - 直接访问，无需再次登录

3. **认证过期处理**
   - 清除本地存储，重定向到登录

4. **公开路由访问**
   - 访问 `/login` 或 `/404` 无需认证

## 开发时间估算

- **阶段1**: 30分钟 (类型定义和工具函数)
- **阶段2**: 45分钟 (路由守卫组件)
- **阶段3**: 30分钟 (登录页面)
- **阶段4**: 15分钟 (路由配置)
- **阶段5**: 30分钟 (测试和优化)

**总计**: 约2.5小时

## 风险和对策

1. **localStorage限制**: 添加错误处理，考虑sessionStorage备选
2. **路由守卫性能**: 使用React.memo优化组件
3. **用户体验**: 添加加载状态和错误提示
4. **安全性**: 前端只做格式验证，真实权限由后端验证

## 后续扩展

- 角色权限控制
- 刷新token机制
- OAuth集成
- 用户管理后台
