import type {
  RouteDefinition,
  RouteRegistrar,
  RouteUnregistrar,
  RouterConfig,
} from './types';
import { RouteCacheManager } from './RouteCache';

/**
 * 路由管理器类
 * 负责管理应用的所有路由，包括注册、注销、获取路由信息等操作
 */
export class RouteManager {
  /**
   * 存储所有注册的路由，使用 Map 数据结构以路径为键
   */
  private routes: Map<string, RouteDefinition> = new Map();

  /**
   * 路由缓存管理器实例
   */
  private cache: RouteCacheManager;

  /**
   * 路由变化监听器集合
   */
  private listeners: Set<() => void> = new Set();

  /**
   * 路由配置对象
   */
  private config: RouterConfig;

  /**
   * 构造函数
   * @param config - 路由配置对象，可选参数
   */
  constructor(config: RouterConfig = {}) {
    // 设置默认配置并合并用户配置
    this.config = {
      enableCache: true, // 默认启用缓存
      maxCacheSize: 50, // 默认最大缓存大小
      ...config, // 合并用户配置
    };
    // 初始化缓存管理器
    this.cache = new RouteCacheManager(this.config.maxCacheSize);
  }

  /**
   * 注册新路由
   * @param route - 路由定义对象
   */
  register: RouteRegistrar = (route: RouteDefinition) => {
    // 检查路由是否已存在，如果存在则发出警告
    if (this.routes.has(route.path)) {
      console.warn(`Route ${route.path} already exists, overwriting...`);
      return;
    }

    // 将路由添加到路由映射中
    this.routes.set(route.path, route);

    // 如果启用了缓存，清除该路径的缓存
    if (this.config.enableCache) {
      this.cache.delete(route.path);
    }

    // 通知所有监听器路由已更新
    this.notifyListeners();
  };

  /**
   * 注销路由
   * @param path - 要注销的路由路径
   * @returns 如果路由存在并被成功注销则返回 true，否则返回 false
   */
  unregister: RouteUnregistrar = (path: string) => {
    // 尝试从路由映射中删除路由
    const existed = this.routes.delete(path);
    if (existed) {
      // 如果路由存在，清除其缓存
      this.cache.delete(path);
      // 通知所有监听器路由已更新
      this.notifyListeners();
    }
    // 返回路由是否存在
    return existed;
  };

  /**
   * 获取所有路由
   * @returns 包含所有路由定义的数组
   */
  getRoutes(): RouteDefinition[] {
    return Array.from(this.routes.values());
  }

  /**
   * 获取单个路由
   * @param path - 路由路径
   * @returns 路由定义对象，如果路由不存在则返回 undefined
   */
  getRoute(path: string): RouteDefinition | undefined {
    return this.routes.get(path);
  }

  /**
   * 检查路由是否存在
   * @param path - 路由路径
   * @returns 如果路由存在则返回 true，否则返回 false
   */
  hasRoute(path: string): boolean {
    return this.routes.has(path);
  }

  /**
   * 获取缓存管理器
   * @returns 路由缓存管理器实例
   */
  getCache(): RouteCacheManager {
    return this.cache;
  }

  /**
   * 清除所有路由和缓存
   */
  clear(): void {
    // 清空路由映射
    this.routes.clear();
    // 清空缓存
    this.cache.clear();
    // 通知所有监听器路由已更新
    this.notifyListeners();
  }

  /**
   * 批量注册路由
   * @param routes - 路由定义对象数组
   */
  registerRoutes(routes: RouteDefinition[]): void {
    // 遍历路由数组并逐个注册
    routes.forEach(route => this.register(route));
  }

  /**
   * 订阅路由变化
   * @param callback - 路由变化时调用的回调函数
   * @returns 取消订阅的函数
   */
  subscribe(callback: () => void): () => void {
    // 添加监听器
    this.listeners.add(callback);
    // 返回取消订阅的函数
    return () => this.listeners.delete(callback);
  }

  /**
   * 获取路由配置
   * @returns 路由配置对象的副本
   */
  getConfig(): RouterConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param newConfig - 新的配置对象
   */
  updateConfig(newConfig: Partial<RouterConfig>): void {
    // 更新配置
    this.config = { ...this.config, ...newConfig };

    // 如果新的最大缓存大小被设置，重新初始化缓存管理器
    if (newConfig.maxCacheSize !== undefined) {
      this.cache = new RouteCacheManager(newConfig.maxCacheSize);
    }
  }

  /**
   * 通知所有监听器路由已更新
   * 私有方法，仅供内部使用
   */
  private notifyListeners(): void {
    // 遍历所有监听器并调用它们
    this.listeners.forEach(callback => callback());
  }
}

/**
 * 创建全局路由管理器实例
 */
export const routeManager = new RouteManager();
