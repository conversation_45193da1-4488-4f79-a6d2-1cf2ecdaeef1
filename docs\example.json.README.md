# JSON 路由配置示例文件

## 概述

`example.json` 是一个用于演示动态路由加载功能的示例JSON配置文件。它展示了如何通过JSON格式定义路由配置，以便在运行时动态注册到应用中。

## 文件结构

该文件包含一个路由配置对象数组，每个对象代表一个路由规则。

## 路由对象结构

### 必需字段

- `path` (string): 路由路径，例如 "/dashboard"
- `component` (string): 对应的React组件名称，必须与 `src/pages/` 目录下的组件文件名匹配

### 可选字段

- `name` (string): 路由显示名称，用于导航菜单
- `exact` (boolean): 精确匹配标识，默认为 `false`
- `meta` (object): 路由元数据对象，包含以下可选属性：
  - `title` (string): 页面标题
  - `icon` (string): 路由图标，可以是emoji或图标类名
  - `requiresAuth` (boolean): 是否需要身份验证，默认为 `false`

## 示例解析

### 仪表盘路由

```json
{
  "path": "/dashboard",
  "component": "Home",
  "name": "仪表盘",
  "meta": {
    "title": "用户仪表盘",
    "icon": "📊",
    "requiresAuth": true
  }
}
```

- 路径：`/dashboard`
- 组件：`Home` (对应 `src/pages/Home.tsx`)
- 名称：`仪表盘`
- 元数据：
  - 标题：`用户仪表盘`
  - 图标：`📊`
  - 需要身份验证：`true`

### 个人资料路由

```json
{
  "path": "/profile",
  "component": "About",
  "name": "个人资料",
  "meta": {
    "title": "个人资料页面",
    "icon": "👤"
  }
}
```

- 路径：`/profile`
- 组件：`About` (对应 `src/pages/About.tsx`)
- 名称：`个人资料`
- 元数据：
  - 标题：`个人资料页面`
  - 图标：`👤`

### 设置路由

```json
{
  "path": "/settings",
  "component": "Settings",
  "name": "设置",
  "meta": {
    "title": "系统设置",
    "icon": "⚙️"
  }
}
```

- 路径：`/settings`
- 组件：`Settings` (需要确保 `src/pages/Settings.tsx` 存在)
- 名称：`设置`
- 元数据：
  - 标题：`系统设置`
  - 图标：`⚙️`

### 帮助中心路由

```json
{
  "path": "/help",
  "component": "Contact",
  "name": "帮助中心",
  "exact": true,
  "meta": {
    "title": "帮助与支持",
    "icon": "❓"
  }
}
```

- 路径：`/help`
- 组件：`Contact` (对应 `src/pages/Contact.tsx`)
- 名称：`帮助中心`
- 精确匹配：`true`
- 元数据：
  - 标题：`帮助与支持`
  - 图标：`❓`

## 使用方法

1. 通过JSON路由加载器组件加载此文件
2. 系统会自动解析路由配置并注册到路由管理器
3. 注册的路由将立即在应用中生效

## 注意事项

1. 组件名称必须与 `src/pages/` 目录下的文件名匹配（不包括 `.tsx` 扩展名）
2. 如果指定的组件不存在，系统将使用 `NotFound` 组件作为后备
3. 路由路径应遵循React Router的路径匹配规则
4. 元数据字段可根据应用需求自定义扩展
