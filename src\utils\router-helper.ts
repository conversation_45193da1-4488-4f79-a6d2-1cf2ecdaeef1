// src/utils/router-helper.ts (修改后)

import type { To, NavigateOptions, Location } from 'react-router-dom'; // <--- 注意这里的 `type`
import type { NavigateFunction } from 'react-router'; // <--- 注意这里的 `type`

// 存储 navigate 函数和 location 对象的变量
let globalNavigate: NavigateFunction | null = null;
let globalLocation: Location | null = null;

/**
 * 在 React 组件中调用此函数，用于设置全局可用的 navigate 和 location 实例。
 * 必须在 <BrowserRouter> 或 <RouterProvider> 内部的组件中调用。
 */
export const setGlobalRouterInstances = (navigate: NavigateFunction, location: Location) => {
    globalNavigate = navigate;
    globalLocation = location;
};

/**
 * 获取当前的 location 对象。
 * 可以在非 React 组件环境中使用。
 * @returns {Location | null} 当前的 location 对象，如果尚未设置则为 null。
 */
export const getGlobalLocation = (): Location | null => {
    return globalLocation;
};

/**
 * 执行路由跳转。
 * 可以在非 React 组件环境中使用。
 * @param {To} to 要跳转到的路径。
 * @param {NavigateOptions} [options] 跳转选项。
 */
export const globalNavigateTo = (to: To, options?: NavigateOptions) => {
    if (globalNavigate) {
        globalNavigate(to, options);
    } else {
        console.warn('globalNavigate is not initialized. Cannot navigate:', to);
    }
};
