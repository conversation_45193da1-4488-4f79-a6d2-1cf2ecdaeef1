# 动态路由布局系统重构文档

## 概述

本文档记录了项目动态路由系统的重构过程，实现了两种不同布局的条件应用机制，支持运行时路由注入到指定布局。

## 重构目标

- ✅ 分析现有路由系统结构
- ✅ 实现两种布局的条件应用
- ✅ 支持运行时动态路由注入
- ✅ 修复路由系统缺陷
- ✅ 保持向后兼容性

## 系统架构变更

### 1. 新增布局类型系统

#### 类型定义 (`src/router/types.ts`)

```typescript
export const LayoutType = {
  DEFAULT: 'default',
  ADMIN: 'admin',
  AUTH: 'auth',
} as const;

export type LayoutType = (typeof LayoutType)[keyof typeof LayoutType];

// 路由定义扩展
export interface RouteDefinition {
  path: string;
  component: () => Promise<{ default: ComponentType }>;
  name?: string;
  exact?: boolean;
  meta?: Record<string, any>;
  layout?: LayoutType; // 新增布局类型字段
}
```

### 2. 布局组件实现

#### 默认布局 (`src/layouts/DefaultLayout.tsx`)

- 标准页面布局
- 包含顶部导航栏和页脚
- 适用于常规页面

#### 管理员布局 (`src/layouts/AdminLayout.tsx`)

- 管理后台布局
- 包含侧边栏导航和顶部工具栏
- 适用于管理功能页面

### 3. 路由系统重构

#### 核心变更 (`src/router/LazyRouter.tsx`)

- 使用 `useRoutes` hook 替代嵌套路由
- 按布局类型分组路由
- 动态应用对应布局包装器

#### 路由分组逻辑

```typescript
const routesByLayout = useMemo(() => {
  const grouped: Record<LayoutType, RouteDefinition[]> = {
    default: [],
    admin: [],
    auth: [],
  };

  routes.forEach(route => {
    const layout = route.layout || 'default';
    grouped[layout].push(route);
  });

  return grouped;
}, [routes]);
```

### 4. 动态路由加载增强

#### JSON路由配置支持 (`src/router/dynamicRouteLoader.ts`)

- 支持 `layout` 字段解析
- 验证布局类型有效性
- 保持向后兼容

#### 示例配置 (`src/router/example.json`)

```json
[
  {
    "path": "/admin/dashboard",
    "component": "Example",
    "name": "管理仪表板",
    "layout": "admin",
    "meta": {
      "title": "管理仪表板",
      "icon": "📊"
    }
  }
]
```

## 使用指南

### 1. 运行时路由注入

#### 通过JSON加载器

```json
[
  {
    "path": "/new-page",
    "component": "Home",
    "layout": "admin",
    "name": "新页面"
  }
]
```

#### 通过代码注册

```typescript
import { routeManager } from './router/RouteManager';

routeManager.register({
  path: '/dynamic-page',
  component: () => import('./pages/DynamicPage'),
  layout: 'admin',
  name: '动态页面',
});
```

### 2. 布局测试工具

#### LayoutTest 组件

- 实时添加测试路由
- 支持两种布局类型
- 一键清除测试路由

#### 使用界面

- 点击"添加默认布局路由"按钮
- 点击"添加管理员布局路由"按钮
- 查看实时路由列表

### 3. 路由验证

#### 验证规则

- `path` 和 `component` 为必填字段
- `layout` 必须是 'default'、'admin' 或 'auth'
- 组件名称必须在可用组件列表中

## 向后兼容性

### 保持的功能

- 所有现有路由继续使用默认布局
- 路由管理器 API 保持不变
- 动态路由加载机制兼容
- 缓存和错误处理机制不变

### 新增功能

- 布局类型选择
- 运行时布局切换
- 布局特定的路由分组

## 性能优化

### 1. 路由缓存

- 按布局类型缓存已加载路由
- 避免重复加载相同组件

### 2. 分组优化

- 使用 `useMemo` 优化路由分组计算
- 减少不必要的重渲染

### 3. 代码分割

- 布局组件按需加载
- 路由组件懒加载

## 测试验证

### 1. 功能测试

- ✅ 默认布局路由正常加载
- ✅ 管理员布局路由正常加载
- ✅ 运行时路由注入功能
- ✅ 布局切换功能
- ✅ 向后兼容性验证

### 2. 边界测试

- ✅ 无效布局类型处理
- ✅ 重复路由路径处理
- ✅ 缺失组件错误处理

## 文件结构变更

```
src/
├── layouts/
│   ├── DefaultLayout.tsx    # 默认布局组件
│   └── AdminLayout.tsx      # 管理员布局组件
├── router/
│   ├── types.ts            # 扩展的路由类型定义
│   ├── LazyRouter.tsx      # 重构的路由器组件
│   └── dynamicRouteLoader.ts # 增强的动态路由加载器
├── components/
│   ├── LayoutTest.tsx      # 布局测试工具
│   └── JSONRouteLoader.tsx # 增强的JSON路由加载器
└── pages/
    └── TestLayout.tsx      # 测试页面组件
```

## 使用示例

### 1. 添加管理员布局路由

```json
[
  {
    "path": "/admin/users",
    "component": "Contact",
    "layout": "admin",
    "name": "用户管理"
  }
]
```

### 2. 添加默认布局路由

```json
[
  {
    "path": "/user/profile",
    "component": "About",
    "layout": "default",
    "name": "用户资料"
  }
]
```

## 注意事项

1. 布局类型必须是预定义的值之一
2. 路由路径应避免冲突
3. 组件名称必须存在于可用组件列表中
4. 测试路由可通过 LayoutTest 组件清除

## 后续扩展

- 支持更多布局类型
- 布局切换动画
- 布局权限控制
- 布局主题定制
