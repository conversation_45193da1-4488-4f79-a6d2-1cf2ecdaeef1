import axios from 'axios';
import { clearAuth, getToken, setRefreshToken, setToken } from '../auth';
import { getGlobalLocation } from '../router-helper';
import { message as GlobalMessage } from 'antd';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use(
  config => {
    // 添加请求头，例如：Authorization
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    const currentLocation = getGlobalLocation();
    if (currentLocation) {
      config.headers['X-Current-Pathname'] = currentLocation.pathname;
    }

    return config;
  },
  error => Promise.reject(error)
);

// 移除 axios 默认的 catch，让 TanStack Query 来处理错误
// 或者 这里添加一个通用的 interceptor 来格式化错误
apiClient.interceptors.response.use(
  response => {

    if (response.headers['access-token']) {
      setToken(response.headers['access-token'])
    }

    if (response.headers['x-access-token']) {
      setRefreshToken(response.headers['x-access-token'])
    }

    if (response.status === 401) {
      clearAuth()
      return Promise.reject(new Error('未未授权'))
    }

    return response.data;
  },
  error => {
    // 统一处理错误，抛出一个 Error 对象
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unexpected error occurred';

    GlobalMessage.error(message);
    return Promise.reject(new Error(message));
  }
);

declare module 'axios' {
  interface AxiosInstance {
    post<T = any, R = T, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
    get<T = any, R = T, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
  }
}


export default apiClient;
