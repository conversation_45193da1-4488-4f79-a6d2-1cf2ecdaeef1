import { useMsal } from "@azure/msal-react";
import { useUserStore } from "../stores/useUserStore";
import { useEffect, useState } from "react";
import { clearAuth, isLocalAuthenticated, isValidToken } from "../utils/auth";
import { registerJSONRoutes } from "../router/dynamicRouteLoader";
import { useRequest } from "ahooks";
import { getUserMenus } from "../api/userApi";
import LoadingSpinner from "./LoadingSpinner";



interface InitAuthProps {
    children: React.ReactNode;
}

export const InitAuth: React.FC<InitAuthProps> = ({ children }) => {

    const { accounts } = useMsal();
    const { user, userMenus, setUserMenus } = useUserStore();
    const [isLoading, setIsLoading] = useState(true);

    const { run, loading } = useRequest(getUserMenus, {
        manual: true,
        onSuccess: (response) => {
            setUserMenus(response)
            registerJSONRoutes(response)
            setIsLoading(false);
        }
    })

    useEffect(() => {
        // 当前时 AAD 还是 本地登录
        if (accounts.length > 0) {
            console.log("AAD 登录")
            console.log("🚀 ~ InitAuth ~ accounts:", accounts[0])

        } else {
            console.log("本地登录")
            // 判断 user 是否为空, 且 校验 Token 
            if (user && isLocalAuthenticated()) {
                if (userMenus.length === 0) {
                    run(user?.userId!)
                } else {
                    console.log("加载菜单", userMenus)
                    registerJSONRoutes(userMenus)
                    setIsLoading(false);
                }

            } else {
                console.log("用户未登录")
                clearAuth()
                setIsLoading(false);
            }
        }

    }, [])

    if (isLoading || loading) {
        return <LoadingSpinner fullScreen={true} />;
    }

    return <>{children}</>;
};