/**
 * App 组件是整个应用的根组件
 * 负责初始化路由配置、设置全局布局和渲染核心组件
 *
 * 设计决策：
 * 1. 使用 LazyRouter 实现代码分割和懒加载，提高应用性能
 * 2. 在模块加载时注册基础路由，确保路由在 LazyRouter 渲染前可用
 * 3. 采用模块化设计，将导航、路由、调试等功能分离到独立组件
 * 4. 支持多种布局的条件应用
 * 5. 提供运行时路由注入功能
 *
 * 性能考虑：
 * - 启用路由缓存以减少重复加载
 * - 使用懒加载避免初始加载过多代码
 * - 支持动态路由注册和注销
 */
// LazyRouter 组件用于实现懒加载路由，routeManager 用于管理路由
import LazyRouter, { routeManager } from './router/LazyRouter';
// LoadingSpinner 组件用于在路由加载时显示加载状态
import LoadingSpinner from './components/LoadingSpinner';
// baseRoutes 包含应用的基础路由配置
import { baseRoutes } from './router/routeConfig';
// 导入应用的全局样式
import './App.css';
import { InitAuth } from './components/InitAuth';

// 在模块加载时注册基础路由，确保路由在 LazyRouter 渲染前可用
baseRoutes.forEach(route => {
  routeManager.register(route);
});



/**
 * App 组件是整个应用的根组件
 * 负责初始化路由配置、设置全局布局和渲染核心组件
 *
 * 设计决策：
 * 1. 使用 LazyRouter 实现代码分割和懒加载，提高应用性能
 * 2. 通过 useEffect 在组件挂载时注册基础路由
 * 3. 采用模块化设计，将导航、路由、调试等功能分离到独立组件
 * 4. 支持多种布局的条件应用
 * 5. 提供运行时路由注入功能
 *
 * 性能考虑：
 * - 启用路由缓存以减少重复加载
 * - 使用懒加载避免初始加载过多代码
 * - 支持动态路由注册和注销
 */
function App() {
  return (
    <InitAuth>
      <LazyRouter
        config={{
          // 使用 LoadingSpinner 组件显示加载状态
          loadingComponent: LoadingSpinner,
          // 启用路由缓存以提高性能
          enableCache: true,
          // 设置最大缓存路由数量为 50
          maxCacheSize: 50,
        }
        }
      ></LazyRouter >
    </InitAuth>
  );
}

export default App;
