import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import { useMsal } from '@azure/msal-react';
import { loginRequest } from '../utils/authConfig';
import { LoginApi } from '../api/loginApi';
import { useUserStore } from '../stores/useUserStore';
import { useNavigate } from 'react-router-dom';

const Login: React.FC = () => {
  const { instance } = useMsal();
  const navigate = useNavigate()
  const [loginMethod, setLoginMethod] = useState<'local' | 'aad'>('local');
  const { setUser, user, isAuthenticated } = useUserStore();
  const { loading, runAsync } = useRequest(LoginApi, {
    manual: true,
    onSuccess: (response) => {
      setUser({ ...response, loginMethod: 'local' });
      navigate('/')
    }
  });



  const onSubmit = async () => {
    if (loginMethod === 'local') {
      await runAsync('zhannan97', 'zhannan');
    } else {
      instance.loginRedirect(loginRequest).then(response => {
        console.log(response);
      });
    }


  };

  return (
    <div
      className="login-page"
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        fontFamily: 'Segoe UI, Roboto, Helvetica, Arial, sans-serif',
      }}
    >
      <div
        style={{
          display: 'flex',
          backgroundColor: 'white',
          borderRadius: '16px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          overflow: 'hidden',
          maxWidth: '900px',
          width: '100%',
          minHeight: '500px',
        }}
      >
        {/* 左侧装饰区域 */}
        <div
          style={{
            flex: 1,
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            padding: '3rem 2rem',
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center',
          }}
        >
          <div style={{ marginBottom: '2rem' }}></div>
          <h2
            style={{
              margin: '0 0 1rem 0',
              fontSize: '2rem',
              fontWeight: '600',
            }}
          >
            欢迎回来
          </h2>
          <p
            style={{
              margin: 0,
              fontSize: '1rem',
              lineHeight: '1.6',
              opacity: 0.9,
              maxWidth: '300px',
            }}
          >
            登录您的账户以继续访问我们的平台
          </p>
        </div>

        {/* 右侧登录表单 */}
        <div
          style={{
            flex: 1,
            padding: '3rem 2.5rem',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <h1
              style={{
                margin: '0 0 0.5rem 0',
                color: '#333',
                fontSize: '1.75rem',
                fontWeight: '600',
              }}
            >
              登录账户
            </h1>
            <p
              style={{
                color: '#666',
                margin: 0,
                fontSize: '0.95rem',
              }}
            >
              选择您的登录方式
            </p>
          </div>

          <div
            style={{
              display: 'flex',
              marginBottom: '2rem',
              backgroundColor: '#f5f7fa',
              padding: '0.25rem',
              borderRadius: '12px',
            }}
          >
            <button
              type="button"
              onClick={() => setLoginMethod('local')}
              style={{
                flex: 1,
                padding: '0.75rem',
                border: 'none',
                backgroundColor:
                  loginMethod === 'local' ? 'white' : 'transparent',
                color: loginMethod === 'local' ? '#4a6cf7' : '#666',
                cursor: 'pointer',
                fontWeight: loginMethod === 'local' ? '600' : '500',
                borderRadius: '10px',
                transition: 'all 0.3s ease',
                boxShadow:
                  loginMethod === 'local'
                    ? '0 2px 8px rgba(0,0,0,0.08)'
                    : 'none',
              }}
            >
              账户密码登录
            </button>
            <button
              type="button"
              onClick={() => setLoginMethod('aad')}
              style={{
                flex: 1,
                padding: '0.75rem',
                border: 'none',
                backgroundColor:
                  loginMethod === 'aad' ? 'white' : 'transparent',
                color: loginMethod === 'aad' ? '#4a6cf7' : '#666',
                cursor: 'pointer',
                fontWeight: loginMethod === 'aad' ? '600' : '500',
                borderRadius: '10px',
                transition: 'all 0.3s ease',
                boxShadow:
                  loginMethod === 'aad' ? '0 2px 8px rgba(0,0,0,0.08)' : 'none',
              }}
            >
              Microsoft登录
            </button>
          </div>

          {/* {error && (
            <div
              style={{
                backgroundColor: '#fef2f2',
                color: '#dc2626',
                padding: '0.875rem',
                borderRadius: '8px',
                marginBottom: '1.5rem',
                border: '1px solid #fecaca',
                fontSize: '0.875rem',
              }}
            >
              {error}
            </div>
          )} */}

          <form style={{ flex: 1 }}>
            {loginMethod === 'local' ? (
              <>
                <div style={{ marginBottom: '1.25rem' }}>
                  <label
                    style={{
                      display: 'block',
                      marginBottom: '0.5rem',
                      color: '#333',
                      fontWeight: '500',
                      fontSize: '0.9rem',
                    }}
                  >
                    用户名
                  </label>
                  <input
                    type="text"
                    required
                    placeholder="请输入用户名"
                    style={{
                      width: '100%',
                      padding: '0.875rem 1rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      boxSizing: 'border-box',
                      fontSize: '1rem',
                      transition: 'border-color 0.2s, box-shadow 0.2s',
                      outline: 'none',
                    }}
                    onFocus={e => {
                      e.target.style.borderColor = '#4a6cf7';
                      e.target.style.boxShadow =
                        '0 0 0 3px rgba(74, 108, 247, 0.1)';
                    }}
                    onBlur={e => {
                      e.target.style.borderColor = '#d1d5db';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div style={{ marginBottom: '1.5rem' }}>
                  <label
                    style={{
                      display: 'block',
                      marginBottom: '0.5rem',
                      color: '#333',
                      fontWeight: '500',
                      fontSize: '0.9rem',
                    }}
                  >
                    密码
                  </label>
                  <input
                    type="password"
                    required
                    placeholder="请输入密码"
                    style={{
                      width: '100%',
                      padding: '0.875rem 1rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      boxSizing: 'border-box',
                      fontSize: '1rem',
                      transition: 'border-color 0.2s, box-shadow 0.2s',
                      outline: 'none',
                    }}
                    onFocus={e => {
                      e.target.style.borderColor = '#4a6cf7';
                      e.target.style.boxShadow =
                        '0 0 0 3px rgba(74, 108, 247, 0.1)';
                    }}
                    onBlur={e => {
                      e.target.style.borderColor = '#d1d5db';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                </div>
              </>
            ) : (
              <div
                style={{
                  textAlign: 'center',
                  padding: '1.75rem 1rem',
                  backgroundColor: '#f8fafc',
                  borderRadius: '12px',
                  marginBottom: '2rem',
                  border: '1px dashed #cbd5e1',
                }}
              >
                <div style={{ marginBottom: '1.5rem' }}></div>
                <h3
                  style={{
                    margin: '0 0 0.5rem 0',
                    color: '#334155',
                    fontWeight: '600',
                  }}
                >
                  Microsoft账户登录
                </h3>
                <p
                  style={{
                    color: '#64748b',
                    margin: '0 0 1.5rem 0',
                    lineHeight: '1.6',
                  }}
                >
                  使用您的Microsoft工作或学校账户登录
                </p>
              </div>
            )}

            <Button
              type="primary"
              style={{ height: '56px', fontWeight: 'bold', fontSize: '1rem' }}
              block
              loading={loading}
              onClick={onSubmit}
            >
              {loginMethod === 'local' ? '登录' : '使用Microsoft账户登录'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
