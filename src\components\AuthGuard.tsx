import { useMsal } from '@azure/msal-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { getToken, isLocalAuthenticated } from '../utils/auth';
import LoadingSpinner from './LoadingSpinner';
import { useUserStore } from '../stores/useUserStore';
import { routeManager } from '../router/RouteManager';
import { useRequest } from 'ahooks';
import { getUserMenus } from '../api/userApi';
import { registerJSONRoutes } from '../router/dynamicRouteLoader';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, userMenus, setUserMenus } = useUserStore();
  const { accounts } = useMsal();
  const [isLoading, setIsLoading] = useState(true);

  const { run, loading } = useRequest(getUserMenus, {
    manual: true,
    onSuccess: (response) => {
      setUserMenus(response)
      registerJSONRoutes(response)
      setIsLoading(false);
    }
  })

  // 获取当前路由的 meta 
  const meta = routeManager.getRoute(location.pathname)?.meta;

  const checkAAD = () => {
    if (accounts.length > 0) {
      return true;
    } else {
      return false;
    }
  };

  useEffect(() => {
    // 如果是公开页面，不需要检查认证
    if (meta?.public) {
      setIsLoading(false);
      return;
    }


    // AAD 登录
    if (!checkAAD()) {
      if (isLocalAuthenticated() && userMenus.length === 0) {
        run(user?.userId!)
      } else if (isLocalAuthenticated() && userMenus.length > 0) {
        registerJSONRoutes(userMenus)
        setIsLoading(false);
      } else {
        navigate("/login")
      }
    } else {

    }
  }, [meta, navigate]);


  if (isLoading || loading) {
    return <LoadingSpinner fullScreen={true} />;
  }

  // if (!isAuthenticated()) {
  // 	return (
  // 		<Navigate
  // 			to={`/login?redirect=${encodeURIComponent(location.pathname + location.search)}`}
  // 			replace
  // 		/>
  // 	);
  // }

  return <>{children}</>;
};