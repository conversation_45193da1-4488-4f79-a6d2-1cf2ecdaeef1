/**
 * 用户认证相关类型定义
 */
export interface AuthUser {
  id: string;
  username: string;
  email?: string;
  name?: string;
  avatar?: string;
  roles?: string[];
}

export interface AuthToken {
  userId: string;
  account: string;
  expiresAt?: number;
  refreshToken?: string;
}

export interface AuthCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  token: AuthToken | null;
  isLoading: boolean;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: AuthUser;
  message?: string;
}

export interface AuthError {
  code: string;
  message: string;
  details?: any;
}
