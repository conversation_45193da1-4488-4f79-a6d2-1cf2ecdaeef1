import type { RouteDefinition } from './types';
import React from 'react';

/**
 * 基础路由配置数组
 * 定义了应用的基本页面路由，包括首页、关于、联系和404页面
 */
export const baseRoutes: RouteDefinition[] = [
  {
    path: '/',
    name: 'home',
    component: () => import('../pages/Home'),
    meta: {
      title: '首页',
      icon: '🏠',
      requiresAuth: true,
    },
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../pages/Login'),
    meta: {
      title: '登录',
      icon: '🔑',
      public: true, // 允许公开访问
    },
  },
  {
    path: '/logout',
    name: 'logout',
    component: () => import('../pages/Logout'),
    meta: {
      title: '登出',
      icon: '🚪',
      public: true,
    },
  },
  {
    // 404页面路由配置
    path: '/404', // 路由路径
    name: '404', // 路由名称
    component: () => import('../pages/NotFound'), // 组件懒加载
    meta: {
      title: '页面未找到', // 页面标题
      icon: '❌', // 页面图标
      public: true,
    },
  },
];

/**
 * 创建延迟加载路由的工具函数
 * @param path - 路由路径
 * @param name - 路由名称
 * @param importFn - 组件懒加载函数
 * @param meta - 路由元信息（可选）
 * @returns 路由定义对象
 */
export const createLazyRoute = (
  path: string,
  name: string,
  importFn: () => Promise<{ default: React.ComponentType }>,
  meta?: Record<string, any>
): RouteDefinition => ({
  path,
  name,
  component: importFn,
  meta,
});

/**
 * 注册基础路由的工具函数
 * 异步加载 LazyRouter 模块并注册所有基础路由
 */
export const registerBaseRoutes = () => {
  import('./LazyRouter').then(module => {
    baseRoutes.forEach(route => {
      module.routeManager.register(route);
    });
  });
};
