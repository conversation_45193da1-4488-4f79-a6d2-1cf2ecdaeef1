import { Link, Navigate } from 'react-router-dom';
import { isLocalAuthenticated } from '../utils/auth';

function NotFound() {
  if (!isLocalAuthenticated()) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        textAlign: 'center',
      }}
    >
      <h1 style={{ fontSize: '6rem', margin: 0, color: '#ff4757' }}>404</h1>
      <h2 style={{ fontSize: '2rem', margin: '1rem 0' }}>页面未找到</h2>
      <p style={{ fontSize: '1.2rem', color: '#666', marginBottom: '2rem' }}>
        抱歉，您访问的页面不存在。
      </p>
      <Link
        to="/"
        style={{
          padding: '0.75rem 1.5rem',
          backgroundColor: '#007bff',
          color: 'white',
          textDecoration: 'none',
          borderRadius: '4px',
          fontSize: '1.1rem',
        }}
      >
        返回首页
      </Link>
    </div>
  );
}

export default NotFound;
