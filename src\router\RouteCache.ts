// 导入路由相关类型定义
import type { LoadedRoute, RouteCache } from './types';

/**
 * RouteCacheManager 路由缓存管理器
 * 实现基于LRU（最近最少使用）策略的路由缓存机制
 * 用于存储和管理已加载的路由组件，提高应用性能
 *
 * 核心功能：
 * 1. 存储已加载的路由组件
 * 2. 实现LRU缓存淘汰策略
 * 3. 提供缓存的基本操作（增删查改）
 * 4. 限制缓存大小防止内存泄漏
 *
 * 设计决策：
 * 1. 使用类实现，封装缓存逻辑
 * 2. 实现LRU算法优化缓存效率
 * 3. 提供最大缓存大小限制
 * 4. 使用访问顺序数组维护LRU状态
 * 5. 提供完整的缓存操作API
 *
 * LRU算法实现：
 * - accessOrder数组维护访问顺序
 * - 最近访问的路由放在数组末尾
 * - 最久未访问的路由位于数组开头
 * - 缓存满时淘汰数组开头的路由
 *
 * 性能考虑：
 * - get操作时间复杂度O(1)
 * - set操作时间复杂度O(n)（最坏情况需要淘汰）
 * - 使用对象存储实现O(1)查找
 * - 限制缓存大小防止内存无限增长
 *
 * 内存管理：
 * - 提供clear方法清空缓存
 * - 提供delete方法删除特定路由
 * - 自动淘汰最久未使用的路由
 *
 * 扩展性：
 * - 可配置最大缓存大小
 * - 提供缓存状态查询方法
 * - 支持与其他缓存系统集成
 */
export class RouteCacheManager {
  /**
   * 路由缓存对象
   * 以路径为键，已加载路由为值
   * 使用对象存储实现O(1)查找时间复杂度
   */
  private cache: RouteCache = {};

  /**
   * 最大缓存大小
   * 限制缓存中存储的路由数量
   * 防止内存无限增长
   */
  private maxSize: number;

  /**
   * 访问顺序数组
   * 维护路由的访问顺序，实现LRU算法
   * 最近访问的路由在数组末尾
   * 最久未访问的路由在数组开头
   */
  private accessOrder: string[] = [];

  /**
   * 构造函数
   * 初始化路由缓存管理器
   *
   * @param maxSize - 最大缓存大小，默认为50
   *
   * 设计考虑：
   * - 默认大小50适合大多数应用场景
   * - 可通过参数自定义缓存大小
   * - 初始化空的缓存和访问顺序数组
   */
  constructor(maxSize = 50) {
    /**
     * 设置最大缓存大小
     * 保存传入的参数值或默认值
     */
    this.maxSize = maxSize;
  }

  /**
   * 获取缓存中的路由
   * 根据路径查找已加载的路由组件
   * 如果找到则更新访问顺序（LRU）
   *
   * @param path - 路由路径
   * @returns 已加载的路由对象或undefined
   *
   * 时间复杂度：O(n)，其中n为访问顺序数组长度
   *
   * 设计考虑：
   * - 实现LRU算法，更新访问顺序
   * - 不存在时返回undefined
   * - 存在时返回路由对象引用
   */
  get(path: string): LoadedRoute | undefined {
    /**
     * 从缓存中获取路由对象
     * 使用对象属性访问实现O(1)时间复杂度
     */
    const route = this.cache[path];

    /**
     * 检查路由是否存在
     * 如果存在则更新访问顺序
     */
    if (route) {
      /**
       * 更新访问顺序（LRU）
       * 将该路由标记为最近访问
       */
      this.updateAccessOrder(path);
    }

    /**
     * 返回路由对象或undefined
     */
    return route;
  }

  /**
   * 设置缓存中的路由
   * 将已加载的路由组件存储到缓存中
   * 如果缓存已满则淘汰最久未使用的路由
   *
   * @param path - 路由路径
   * @param route - 已加载的路由对象
   *
   * 时间复杂度：O(n)，其中n为访问顺序数组长度
   *
   * 设计考虑：
   * - 如果路由已存在则先移除
   * - 检查缓存大小限制
   * - 必要时淘汰最久未使用的路由
   * - 更新访问顺序
   */
  set(path: string, route: LoadedRoute): void {
    /**
     * 检查路由是否已存在
     * 如果存在则先从访问顺序中移除
     * 避免重复条目
     */
    if (this.cache[path]) {
      /**
       * 从访问顺序数组中移除
       * 为更新操作做准备
       */
      this.removeFromAccessOrder(path);
    }

    /**
     * 检查缓存大小限制
     * 如果缓存已满且新路由不存在于缓存中
     * 则淘汰最久未使用的路由
     */
    if (Object.keys(this.cache).length >= this.maxSize && !this.cache[path]) {
      /**
       * 淘汰最久未使用的路由
       * 实现LRU算法的核心逻辑
       */
      this.evictOldest();
    }

    /**
     * 将路由存储到缓存中
     * 使用路径作为键
     */
    this.cache[path] = route;

    /**
     * 将路径添加到访问顺序数组末尾
     * 标记为最近访问
     */
    this.accessOrder.push(path);
  }

  /**
   * 检查缓存中是否存在指定路由
   *
   * @param path - 路由路径
   * @returns 如果存在返回true，否则返回false
   *
   * 时间复杂度：O(1)
   *
   * 设计考虑：
   * - 使用in操作符检查对象属性
   * - 不影响访问顺序
   * - 不触发LRU更新
   */
  has(path: string): boolean {
    /**
     * 检查路径是否存在于缓存对象中
     * 使用in操作符实现O(1)时间复杂度
     */
    return path in this.cache;
  }

  /**
   * 从缓存中删除指定路由
   *
   * @param path - 路由路径
   * @returns 如果删除成功返回true，否则返回false
   *
   * 时间复杂度：O(n)，其中n为访问顺序数组长度
   *
   * 设计考虑：
   * - 同时从缓存对象和访问顺序数组中删除
   * - 不存在时返回false
   * - 存在时删除并返回true
   */
  delete(path: string): boolean {
    /**
     * 检查路由是否存在
     * 如果存在则执行删除操作
     */
    if (path in this.cache) {
      /**
       * 从缓存对象中删除路由
       * 使用delete操作符
       */
      delete this.cache[path];

      /**
       * 从访问顺序数组中移除路径
       * 维护LRU算法的一致性
       */
      this.removeFromAccessOrder(path);

      /**
       * 返回删除成功标识
       */
      return true;
    }

    /**
     * 路由不存在，返回删除失败标识
     */
    return false;
  }

  /**
   * 清空缓存
   * 删除所有已缓存的路由
   * 重置访问顺序数组
   *
   * 时间复杂度：O(1)
   *
   * 设计考虑：
   * - 重新初始化缓存对象
   * - 清空访问顺序数组
   * - 快速重置所有状态
   */
  clear(): void {
    /**
     * 重新初始化缓存对象
     * 删除所有已缓存的路由
     */
    this.cache = {};

    /**
     * 清空访问顺序数组
     * 重置LRU状态
     */
    this.accessOrder = [];
  }

  /**
   * 获取缓存大小
   * 返回当前缓存中的路由数量
   *
   * @returns 缓存中的路由数量
   *
   * 时间复杂度：O(n)，其中n为缓存对象的属性数量
   *
   * 设计考虑：
   * - 使用Object.keys获取属性数量
   * - 不影响访问顺序
   */
  size(): number {
    /**
     * 返回缓存对象的属性数量
     * 即当前缓存中的路由数量
     */
    return Object.keys(this.cache).length;
  }

  /**
   * 获取所有缓存键
   * 返回当前缓存中所有路由路径的数组
   *
   * @returns 路由路径数组
   *
   * 时间复杂度：O(n)，其中n为缓存对象的属性数量
   *
   * 设计考虑：
   * - 使用Object.keys获取所有键
   * - 返回新数组不暴露内部结构
   */
  keys(): string[] {
    /**
     * 返回缓存对象的所有键
     * 即所有已缓存路由的路径
     */
    return Object.keys(this.cache);
  }

  /**
   * 更新访问顺序
   * 将指定路径移到访问顺序数组末尾
   * 标记为最近访问
   *
   * @param path - 路由路径
   *
   * 时间复杂度：O(n)，其中n为访问顺序数组长度
   *
   * 设计考虑：
   * - 先移除再添加确保唯一性
   * - 添加到数组末尾标记为最近访问
   * - 私有方法仅供内部使用
   */
  private updateAccessOrder(path: string): void {
    /**
     * 从访问顺序数组中移除路径
     * 避免重复条目
     */
    this.removeFromAccessOrder(path);

    /**
     * 将路径添加到数组末尾
     * 标记为最近访问
     */
    this.accessOrder.push(path);
  }

  /**
   * 从访问顺序数组中移除路径
   *
   * @param path - 路由路径
   *
   * 时间复杂度：O(n)，其中n为访问顺序数组长度
   *
   * 设计考虑：
   * - 使用indexOf查找位置
   * - 使用splice删除元素
   * - 路径不存在时不执行操作
   * - 私有方法仅供内部使用
   */
  private removeFromAccessOrder(path: string): void {
    /**
     * 查找路径在访问顺序数组中的位置
     * indexOf方法返回元素索引或-1
     */
    const index = this.accessOrder.indexOf(path);

    /**
     * 检查路径是否存在
     * 如果存在则从数组中删除
     */
    if (index > -1) {
      /**
       * 从访问顺序数组中删除路径
       * splice方法修改原数组
       */
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * 淘汰最久未使用的路由
   * 删除访问顺序数组开头的路由
   * 实现LRU算法的核心逻辑
   *
   * 时间复杂度：O(1)
   *
   * 设计考虑：
   * - 从访问顺序数组开头删除
   * - 同时从缓存对象中删除
   * - 数组为空时不执行操作
   * - 私有方法仅供内部使用
   */
  private evictOldest(): void {
    /**
     * 检查访问顺序数组是否为空
     * 如果不为空则执行淘汰操作
     */
    if (this.accessOrder.length > 0) {
      /**
       * 从访问顺序数组开头移除最久未使用的路由路径
       * shift方法返回被移除的元素
       */
      const oldest = this.accessOrder.shift();

      /**
       * 检查是否成功获取到路径
       * 如果获取到则从缓存中删除
       */
      if (oldest) {
        /**
         * 从缓存对象中删除最久未使用的路由
         * 释放内存资源
         */
        delete this.cache[oldest];
      }
    }
  }
}
