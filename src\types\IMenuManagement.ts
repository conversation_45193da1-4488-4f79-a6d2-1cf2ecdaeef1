

export interface IMenuManagement {
    /**
     * 菜单Id
     */
    menuId: string;
    /**
     * 菜单名称
     */
    menuName: string;
    /**
     * 菜单类型
     */
    menuType: number;
    /**
     * 菜单图标
     */
    menuIcon: string;
    /**
     * 菜单路径
     */
    menuPath?: string;
    /**
    * 菜单排序
    */
    menuSort: number;
    /**
     * 菜单状态
     */
    menuStatus: number;
    /**
     * 组件路径
     */
    componentPath?: string;
    /**
     * 页面布局
     */
    layout?: string;
    /**
     * 子菜单
     */
    children?: IMenuManagement[];
}


export interface ITypeShow {
    key: number;
    label: string;
    color: string;
}