// React 导入，用于构建组件
import { useState } from 'react';
// 导入动态路由加载相关的函数
import {
  createRoutesFromJSON,
  registerJSONRoutes,
  getAvailableComponents,
} from '../router/dynamicRouteLoader';
// 导入路由定义类型
import type { JSONRouteConfig, RouteDefinition } from '../router/types';

/**
 * JSONRouteLoaderProps 接口定义了 JSONRouteLoader 组件的属性
 *
 * 设计考虑：
 * - onRoutesLoaded: 可选回调函数，路由加载成功后调用
 * - 支持组件外部监听路由加载事件
 */
interface JSONRouteLoaderProps {
  /**
   * 路由加载成功后的回调函数
   * @param routes - 加载的路由定义数组
   */
  onRoutesLoaded?: (routes: RouteDefinition[]) => void;
}

/**
 * JSONRouteLoader 组件用于从JSON配置动态加载路由
 * 提供文本框输入JSON配置，支持验证和注册路由
 *
 * 核心功能：
 * 1. 解析用户输入的JSON路由配置
 * 2. 验证路由配置格式和必需字段
 * 3. 创建并注册路由到路由管理器
 * 4. 显示可用组件列表供用户参考
 * 5. 提供示例JSON配置加载功能
 * 6. 错误处理和用户反馈
 *
 * 设计决策：
 * 1. 使用 useState 管理组件状态（输入、错误信息）
 * 2. 在组件初始化时获取可用组件列表
 * 3. 提供可视化界面，便于用户操作
 * 4. 实现完整的错误处理流程
 * 5. 支持回调函数通知外部组件路由加载结果
 *
 * 用户体验考虑：
 * - 提供清晰的输入提示和占位符
 * - 显示可用组件列表，帮助用户正确配置
 * - 提供示例加载功能，降低使用门槛
 * - 错误信息高亮显示，便于用户识别问题
 * - 禁用空输入时的加载按钮，防止无效操作
 *
 * 安全性考虑：
 * - 对JSON输入进行解析和验证
 * - 检查必需字段是否存在
 * - 使用try-catch处理解析异常
 *
 * 扩展性：
 * - 通过 onRoutesLoaded 回调支持外部监听
 * - 可轻松添加更多验证规则
 */
export default function JSONRouteLoader({
  onRoutesLoaded,
}: JSONRouteLoaderProps) {
  /**
   * 状态变量：JSON输入框的值
   * 存储用户输入的JSON路由配置字符串
   */
  const [jsonInput, setJsonInput] = useState('');

  /**
   * 状态变量：错误信息
   * 存储解析或验证过程中产生的错误信息
   */
  const [error, setError] = useState<string | null>(null);

  /**
   * 状态变量：可用组件列表
   * 从 dynamicRouteLoader 获取支持的组件名称列表
   * 用于提示用户可用的组件选项
   */
  const [availableComponents] = useState(getAvailableComponents());

  /**
   * 处理加载路由的函数
   * 解析JSON输入，验证格式，创建并注册路由
   *
   * 处理流程：
   * 1. 清除之前的错误信息
   * 2. 解析JSON字符串
   * 3. 验证数组格式和必需字段
   * 4. 创建路由定义对象
   * 5. 注册路由到路由管理器
   * 6. 调用回调函数通知外部组件
   * 7. 清空输入框或显示错误信息
   */
  const handleLoadRoutes = () => {
    try {
      // 清除之前的错误信息
      setError(null);
      // 解析JSON输入
      const jsonRoutes = JSON.parse(jsonInput);

      // 验证JSON必须是数组格式
      if (!Array.isArray(jsonRoutes)) {
        throw new Error('JSON 必须是数组格式');
      }

      /**
       * 验证每个路由项的必需字段
       * 确保每个路由都有 path 和 component 字段
       *
       * 验证规则：
       * - path: 路由路径，必需字段
       * - component: 组件名称，必需字段
       * - layout: 布局类型，可选字段，必须是 'default' | 'admin' | 'auth'
       */
      jsonRoutes.forEach((route: JSONRouteConfig, index: number) => {
        if (!route.path || !route.component) {
          throw new Error(`路由 ${index} 缺少必需的 path 或 component 字段`);
        }

        if (route.layout && !['default', 'blank'].includes(route.layout)) {
          throw new Error(
            `路由 ${index} 的 layout 必须是 'default' 或 'blank'`
          );
        }
      });

      // 从JSON创建路由定义对象
      const routes = createRoutesFromJSON(jsonRoutes);

      /**
       * 注册路由到路由管理器
       * 使新路由在应用中生效
       */
      registerJSONRoutes(jsonRoutes);

      /**
       * 如果提供了回调函数，则调用通知外部组件
       * 传递创建的路由定义数组
       */
      if (onRoutesLoaded) {
        onRoutesLoaded(routes);
      }

      // 清空输入框
      setJsonInput('');
    } catch (err) {
      /**
       * 处理解析或验证错误
       * 提取错误信息并显示给用户
       */
      setError(err instanceof Error ? err.message : 'JSON 解析失败');
    }
  };

  /**
   * 加载示例JSON配置的函数
   * 从 /src/router/example.json 获取示例路由配置
   * 用于演示和帮助用户理解JSON格式
   *
   * 实现细节：
   * - 使用 fetch API 获取示例文件
   * - 解析JSON响应
   * - 格式化JSON字符串并填充到输入框
   * - 处理加载失败的情况
   */
  const loadExample = async () => {
    try {
      /**
       * 发起网络请求获取示例JSON文件
       * 路径为 /src/router/example.json
       */
      const response = await fetch('/src/router/example.json');
      /**
       * 解析响应为JSON对象
       */
      const exampleRoutes = await response.json();
      /**
       * 将JSON对象转换为格式化的字符串
       * 使用2个空格缩进，便于阅读
       * 设置到输入框状态
       */
      setJsonInput(JSON.stringify(exampleRoutes, null, 2));
    } catch (err) {
      /**
       * 处理加载失败的情况
       * 设置错误信息提示用户
       */
      setError('加载示例失败');
    }
  };

  /**
   * 渲染JSON路由加载器界面
   * 包含输入框、按钮和错误显示区域
   */
  return (
    /**
     * 组件主容器
     * 使用边框和背景色区分功能区域
     * 圆角和内边距提升视觉效果
     */
    <div
      style={{
        border: '1px solid #ccc',
        borderRadius: '8px',
        padding: '20px',
        margin: '20px 0',
        backgroundColor: '#f8f9fa',
      }}
    >
      /** * 组件标题 */
      <h3>JSON 路由加载器</h3>
      /** * 可用组件信息显示区域 * 帮助用户了解支持的组件选项 */
      <div style={{ marginBottom: '15px' }}>
        /** * 显示可用组件列表 * 以逗号分隔的形式展示 */
        <p>可用组件: {availableComponents.join(', ')}</p>
        <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
          支持布局类型: default, blank
        </p>
      </div>
      /** * JSON输入文本框 * 支持多行输入，使用等宽字体便于JSON格式查看 */
      <textarea
        value={jsonInput}
        onChange={e => setJsonInput(e.target.value)}
        placeholder='[{"path": "/example", "component": "Home", "name": "示例页面", "layout": "default"}]'
        style={{
          width: '100%',
          minHeight: '200px',
          padding: '10px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          fontFamily: 'monospace',
          fontSize: '14px',
        }}
      />
      /** * 错误信息显示区域 * 当存在错误时显示，使用红色文本和背景突出显示 */
      {error && (
        <div
          style={{
            color: '#dc3545',
            margin: '10px 0',
            padding: '10px',
            backgroundColor: '#f8d7da',
            borderRadius: '4px',
          }}
        >
          {error}
        </div>
      )}
      /** * 操作按钮区域 * 包含加载路由和加载示例两个按钮 */
      <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
        /** * 加载路由按钮 * 点击后调用 handleLoadRoutes 函数 * 当输入为空时禁用
        */
        <button
          onClick={handleLoadRoutes}
          disabled={!jsonInput.trim()}
          style={{
            padding: '8px 16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          加载路由
        </button>
        /** * 加载示例按钮 * 点击后调用 loadExample 函数 *
        用于加载预定义的示例JSON配置 */
        <button
          onClick={loadExample}
          style={{
            padding: '8px 16px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          加载示例
        </button>
      </div>
    </div>
  );
}
