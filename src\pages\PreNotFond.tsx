import { Navigate, useLocation } from 'react-router-dom';
import { isLocalAuthenticated } from '../utils/auth';

function PreNotFond() {
  const location = useLocation();

  // 判断当前用户是否登录
  if (isLocalAuthenticated()) {
    // 判断是否加载路由, 如果没有加载路由则进行路由加载, 否则跳转到404页面
    return <Navigate to={`/404`} replace />;
  }

  return (
    <Navigate
      to={`/login?redirect=${encodeURIComponent(location.pathname + location.search)}`}
      replace
    />
  );
}

export default PreNotFond;
