// React 导入，用于构建函数组件
// useEffect 用于处理副作用
// useState 用于管理组件状态
// useRef 用于创建可变引用
import React, { useEffect, useState, useRef, useMemo } from 'react';
// React Router DOM 导入
// BrowserRouter 提供路由上下文
// Routes 用于定义路由集合
// Route 用于定义单个路由
// Navigate 用于重定向
import {
  BrowserRouter,
  Navigate,
  useLocation,
  useNavigate,
  useRoutes,
} from 'react-router-dom';
// 导入路由相关类型定义
import type { RouteDefinition, RouterConfig, LayoutType } from './types';
// 导入路由管理器实例
import { routeManager } from './RouteManager';
// 导入懒加载路由组件
import LazyRoute from './LazyRoute';
// 导入默认加载组件
import LoadingSpinner from '../components/LoadingSpinner';
// 导入默认错误边界组件
import ErrorBoundary from '../components/ErrorBoundary';
// 导入布局组件
import DefaultLayout from '../layouts/DefaultLayout';
// 导入认证守卫组件
import { AuthGuard } from '../components/AuthGuard';
import PreNotFond from '../pages/PreNotFond';
import { setGlobalRouterInstances } from '../utils/router-helper';

/**
 * LazyRouterProps 接口定义了 LazyRouter 组件的属性
 *
 * 设计考虑：
 * - config: 可选的路由器配置对象
 * - children: 可选的子组件
 * 在路由之前渲染
 */
interface LazyRouterProps {
  /**
   * 可选的路由器配置对象
   * 用于自定义路由器行为
   * 如果未提供则使用默认配置
   */
  config?: RouterConfig;

  /**
   * 可选的子组件
   * 通常包含应用的布局结构
   * 在路由之前渲染
   */
  children?: React.ReactNode;
}

/**
 * 布局包装器组件
 * 根据路由的layout属性选择对应的布局
 */
function LayoutWrapper({
  children,
  layout,
}: {
  children: React.ReactNode;
  layout?: LayoutType;
}) {
  switch (layout) {
    case 'default':
      return <DefaultLayout>{children}</DefaultLayout>;
    default:
      return <>{children}</>;
  }
}

/**
 * 路由内容组件
 * 处理路由的实际渲染和布局应用
 */
function RouteContent() {
  /**
   * 路由状态
   * 存储当前所有注册的路由定义
   * 当路由管理器中的路由变化时更新
   */
  const [routes, setRoutes] = useState<RouteDefinition[]>([]);

  /**
   * 路由加载状态
   * 跟踪路由是否已经加载完成
   */
  const [routesLoaded, setRoutesLoaded] = useState<boolean>(false);

  /**
   * 路由缓存引用
   * 使用useRef创建持久化引用
   * 以路径为键存储已加载的路由对象
   * 在组件重新渲染时保持缓存不变
   */
  const cacheRef = useRef<Record<string, any>>({});

  /**
   * 路由器配置
   */
  const routerConfig = {
    loadingComponent: LoadingSpinner,
    errorComponent: ErrorBoundary,
    enableCache: true,
    maxCacheSize: 50,
  };

  /**
   * useEffect 钩子
   * 处理路由初始化和订阅的副作用
   */
  useEffect(() => {
    console.log('routeManager.getRoutes()', routeManager.getRoutes());
    /**
     * 初始化路由状态
     * 从路由管理器获取当前所有路由
     * 设置到组件状态中
     */
    const initialRoutes = routeManager.getRoutes();
    setRoutes(initialRoutes);
    setRoutesLoaded(true);

    /**
     * 订阅路由变化
     * 当路由管理器中的路由发生变化时调用回调函数
     * 回调函数更新组件状态以触发重新渲染
     */
    const unsubscribe = routeManager.subscribe(() => {
      /**
       * 当路由变化时更新状态
       * 从路由管理器获取最新的路由列表
       */
      setRoutes(routeManager.getRoutes());
    });

    /**
     * 返回清理函数
     * 在组件卸载时取消订阅
     * 防止内存泄漏
     */
    return unsubscribe;
  }, []);

  /**
   * 按布局类型分组的路由
   */
  const routesByLayout = useMemo(() => {
    const grouped: Record<LayoutType, RouteDefinition[]> = {
      default: [],
      blank: [],
    };

    routes.forEach(route => {
      const layout = route.layout || 'blank';
      if (layout in grouped) {
        grouped[layout].push(route);
      } else {
        grouped.blank.push(route);
      }
    });

    return grouped;
  }, [routes]);

  /**
   * 将路由定义转换为React Router路由配置
   */
  const routingConfig = useMemo(() => {
    if (!routesLoaded) {
      return [
        {
          path: '*',
          element: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
              }}
            >
              <routerConfig.loadingComponent />
            </div>
          ),
        },
      ];
    }

    const allRoutes: any[] = [];

    // 为每个布局类型创建路由组
    Object.entries(routesByLayout).forEach(([layoutType, layoutRoutes]) => {
      if (layoutRoutes.length === 0) return;

      // 为每个布局创建路由
      layoutRoutes.forEach(route => {
        const isPublic = route.meta?.public === true;
        const requiresAuth = route.meta?.requiresAuth === true;

        // 检查是否需要认证
        // const needsAuth = requiresAuth && !isPublic;
        const needsAuth = true;

        const routeElement = (
          <LayoutWrapper layout={layoutType as LayoutType}>
            <LazyRoute
              route={route}
              cache={cacheRef}
              loadingComponent={routerConfig.loadingComponent}
              errorComponent={({ error }) => (
                <ErrorBoundary>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: '200px',
                      padding: '2rem',
                    }}
                  >
                    <h3>路由加载失败</h3>
                    <p>{error.message}</p>
                  </div>
                </ErrorBoundary>
              )}
            >
              {loadedRoute => {
                const Component = loadedRoute.component;
                return <Component />;
              }}
            </LazyRoute>
          </LayoutWrapper>
        );

        allRoutes.push({
          path: route.path,
          element: needsAuth ? (
            <AuthGuard>{routeElement}</AuthGuard>
          ) : (
            routeElement
          ),
        });
      });
    });

    // 添加404路由
    allRoutes.push({
      path: '*',
      element: <PreNotFond />,
    });

    return allRoutes;
  }, [routes, routesLoaded, routerConfig]);

  return useRoutes(routingConfig);
}

/**
 * LazyRouter 懒加载路由器组件
 * 提供基于React Router的懒加载路由功能
 * 集成路由管理、缓存和错误处理
 *
 * 核心功能：
 * 1. 集成React Router提供客户端路由
 * 2. 支持路由的动态注册和注销
 * 3. 实现路由组件的懒加载
 * 4. 提供路由缓存机制
 * 5. 集成错误边界处理路由加载错误
 * 6. 支持自定义配置
 * 7. 支持多种布局的条件应用
 *
 * 设计决策：
 * 1. 使用函数组件实现，充分利用React Hooks
 * 2. 集成routeManager实现路由动态管理
 * 3. 使用LazyRoute组件处理路由懒加载
 * 4. 提供默认的加载和错误组件
 * 5. 实现路由缓存提高性能
 * 6. 支持配置覆盖默认行为
 * 7. 根据layout属性自动选择对应布局
 *
 * 性能优化：
 * - 路由缓存避免重复加载相同路由
 * - 懒加载减少初始包大小
 * - 订阅机制确保路由更新的实时性
 * - 条件渲染优化渲染性能
 * - 按布局类型分组路由减少重渲染
 *
 * 错误处理：
 * - 集成ErrorBoundary处理路由加载错误
 * - 提供自定义错误组件支持
 * - 404路由处理未匹配的路径
 *
 * 用户体验考虑：
 * - 提供加载状态反馈
 * - 错误时显示友好的错误界面
 * - 支持自定义加载和错误组件
 * - 快速响应路由变化
 *
 * 扩展性：
 * - 通过config属性支持自定义配置
 * - 支持children属性嵌入布局组件
 * - 可与其他路由系统集成
 * - 支持不同的缓存策略
 * - 支持多种布局类型
 *
 * @param props - LazyRouterProps 属性对象
 * @returns React元素
 */
export default function LazyRouter({ children }: LazyRouterProps) {
  return (
    /**
     * BrowserRouter 组件
     * 提供React Router的路由上下文
     * 所有路由相关组件必须在此组件内
     */
    <BrowserRouter>
      <RouterInitializer />
      {children}
      <RouteContent />
    </BrowserRouter>
  );
}



const RouterInitializer: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 在组件挂载时设置 navigator 和 location
  useEffect(() => {
    setGlobalRouterInstances(navigate, location);
  }, [navigate, location]); // 确保当 navigate 或 location 改变时更新

  // 这里不渲染任何东西，它只负责初始化
  return null;
};

/**
 * 导出路由管理器实例
 * 供外部模块使用路由管理功能
 * 确保全局只有一个路由管理器实例
 */
export { routeManager };

/**
 * 导出路由相关类型定义
 * 供外部模块使用类型检查
 */
export type { RouteDefinition, RouterConfig, LayoutType } from './types';
