// React 导入，用于构建函数组件
// useEffect 用于处理副作用
// useState 用于管理组件状态
// useRef 用于创建可变引用
import React, { useEffect, useState, useRef } from 'react';
// 导入路由相关类型定义
import type { RouteDefinition, LoadedRoute } from './types';

/**
 * LazyRouteProps 接口定义了 LazyRoute 组件的属性
 *
 * 设计考虑：
 * - route: 必需的路由定义对象
 * - cache: 路由缓存引用对象
 * - loadingComponent: 可选的加载状态组件
 * - errorComponent: 可选的错误状态组件
 * - children: 渲染已加载路由的子函数
 */
interface LazyRouteProps {
  /**
   * 路由定义对象
   * 包含路径、组件加载器、名称等路由信息
   */
  route: RouteDefinition;

  /**
   * 路由缓存引用对象
   * 使用 MutableRefObject 确保缓存在组件重新渲染时保持不变
   * 以路径为键存储已加载的路由对象
   */
  cache: React.MutableRefObject<Record<string, LoadedRoute>>;

  /**
   * 可选的加载状态组件
   * 当路由正在加载时显示此组件
   * 如果未提供则不显示任何内容
   */
  loadingComponent?: React.ComponentType;

  /**
   * 可选的错误状态组件
   * 当路由加载失败时显示此组件
   * 接收错误对象作为属性
   * 如果未提供则不显示任何内容
   */
  errorComponent?: React.ComponentType<{ error: Error }>;

  /**
   * 渲染已加载路由的子函数
   * 接收已加载的路由对象作为参数
   * 返回React节点
   */
  children: (route: LoadedRoute) => React.ReactNode;
}

/**
 * LazyRoute 懒加载路由组件
 * 负责动态加载路由组件，提供缓存、错误处理和加载状态管理
 *
 * 核心功能：
 * 1. 动态加载路由组件
 * 2. 缓存已加载的路由以提高性能
 * 3. 处理加载错误并显示错误界面
 * 4. 显示加载状态指示器
 * 5. 支持请求中止以防止竞态条件
 *
 * 设计决策：
 * 1. 使用函数组件实现，充分利用React Hooks
 * 2. 实现缓存机制减少重复加载
 * 3. 使用AbortController处理请求中止
 * 4. 提供可定制的加载和错误组件
 * 5. 使用useEffect管理副作用
 *
 * 性能优化：
 * - 路由缓存避免重复加载相同路由
 * - 请求中止防止不必要的网络请求
 * - 懒加载减少初始包大小
 * - 条件渲染优化渲染性能
 *
 * 错误处理：
 * - 捕获组件加载过程中的错误
 * - 提供自定义错误组件显示错误信息
 * - 区分中止错误和真实错误
 *
 * 用户体验考虑：
 * - 提供加载状态反馈
 * - 错误时显示友好的错误界面
 * - 支持自定义加载和错误组件
 * - 快速响应路由变化
 *
 * 安全性考虑：
 * - 使用AbortController防止内存泄漏
 * - 正确处理异步操作的生命周期
 * - 防止竞态条件导致的状态不一致
 *
 * 扩展性：
 * - 通过props支持自定义组件
 * - 可与其他路由系统集成
 * - 支持不同的缓存策略
 *
 * @param props - LazyRouteProps 属性对象
 * @returns React元素
 */
export default function LazyRoute({
  /**
   * 路由定义对象
   * 包含路径、组件加载器、名称等路由信息
   */
  route,

  /**
   * 路由缓存引用对象
   * 用于存储已加载的路由组件
   */
  cache,

  /**
   * 可选的加载状态组件
   * 当路由正在加载时显示
   */
  loadingComponent: LoadingComponent,

  /**
   * 可选的错误状态组件
   * 当路由加载失败时显示
   */
  errorComponent: ErrorComponent,

  /**
   * 渲染已加载路由的子函数
   * 接收已加载的路由对象作为参数
   */
  children,
}: LazyRouteProps) {
  /**
   * 加载状态
   * true 表示正在加载路由组件
   * false 表示加载完成或未加载
   */
  const [loading, setLoading] = useState(false);

  /**
   * 错误状态
   * null 表示没有错误
   * Error 对象表示加载过程中发生的错误
   */
  const [error, setError] = useState<Error | null>(null);

  /**
   * 已加载路由状态
   * null 表示尚未加载完成
   * LoadedRoute 对象表示已成功加载的路由
   */
  const [loadedRoute, setLoadedRoute] = useState<LoadedRoute | null>(null);

  /**
   * 中止控制器引用
   * 用于中止正在进行的路由加载请求
   * 使用useRef确保在组件重新渲染时保持引用不变
   */
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * useEffect 钩子
   * 处理路由加载的副作用
   *
   * 依赖项：
   * - route.path: 路由路径变化时重新加载
   * - route.component: 组件加载器变化时重新加载
   * - cache: 缓存对象引用
   *
   * 执行流程：
   * 1. 检查缓存中是否已存在路由
   * 2. 如果存在则直接使用缓存
   * 3. 如果不存在则开始加载流程
   * 4. 中止之前的请求
   * 5. 创建新的中止控制器
   * 6. 设置加载状态
   * 7. 执行组件加载
   * 8. 处理加载结果或错误
   * 9. 清理函数中止请求
   */
  useEffect(() => {
    /**
     * 加载路由的异步函数
     * 负责实际的路由组件加载逻辑
     */
    const loadRoute = async () => {
      /**
       * 检查缓存中是否已存在该路由
       * 如果存在则直接使用缓存，避免重复加载
       */
      if (cache.current[route.path]) {
        /**
         * 设置已加载路由状态为缓存中的路由
         * 触发组件重新渲染
         */
        setLoadedRoute(cache.current[route.path]);
        /**
         * 提前返回，避免重复加载
         */
        return;
      }

      /**
       * 取消之前的路由加载请求
       * 防止竞态条件和内存泄漏
       * 当路由快速切换时确保只加载最新的路由
       */
      if (abortControllerRef.current) {
        /**
         * 调用abort方法中止请求
         * 这将导致之前的加载Promise被拒绝
         */
        abortControllerRef.current.abort();
      }

      /**
       * 创建新的中止控制器
       * 用于控制当前路由加载请求
       * 保存到引用中以便后续中止
       */
      abortControllerRef.current = new AbortController();

      /**
       * 设置加载状态为true
       * 触发组件重新渲染以显示加载界面
       */
      setLoading(true);

      /**
       * 清除之前的错误状态
       * 确保错误界面不会持续显示
       */
      setError(null);

      try {
        /**
         * 执行路由组件的动态加载
         * 调用route.component函数返回Promise
         * 该函数通常是由import()动态导入返回的
         */
        const module = await route.component();

        /**
         * 检查请求是否已被中止
         * 如果已被中止则直接返回，避免状态更新
         */
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        /**
         * 创建已加载路由对象
         * 将加载的模块转换为内部路由格式
         */
        const loaded: LoadedRoute = {
          /**
           * 路由路径，从原始路由定义复制
           */
          path: route.path,

          /**
           * 路由组件，从加载的模块中提取默认导出
           */
          component: module.default,

          /**
           * 路由名称，从原始路由定义复制
           */
          name: route.name,

          /**
           * 精确匹配标识，从原始路由定义复制
           */
          exact: route.exact,

          /**
           * 路由元数据，从原始路由定义复制
           */
          meta: route.meta,
        };

        /**
         * 将已加载的路由存储到缓存中
         * 以路径为键，便于后续快速访问
         */
        cache.current[route.path] = loaded;

        /**
         * 设置已加载路由状态
         * 触发组件重新渲染以显示路由内容
         */
        setLoadedRoute(loaded);
      } catch (err) {
        /**
         * 处理加载过程中的错误
         * 检查请求是否已被中止
         * 如果未中止则设置错误状态
         */
        if (!abortControllerRef.current?.signal.aborted) {
          /**
           * 设置错误状态
           * 如果err已经是Error对象则直接使用
           * 否则创建新的Error对象
           */
          setError(
            err instanceof Error ? err : new Error('Failed to load route')
          );
        }
      } finally {
        /**
         * 无论加载成功还是失败都执行的清理逻辑
         * 检查请求是否已被中止
         * 如果未中止则清除加载状态
         */
        if (!abortControllerRef.current?.signal.aborted) {
          /**
           * 设置加载状态为false
           * 触发组件重新渲染以隐藏加载界面
           */
          setLoading(false);
        }
      }
    };

    /**
     * 调用加载路由函数
     * 开始实际的路由加载过程
     */
    loadRoute();

    /**
     * 清理函数
     * 在组件卸载或依赖项变化时执行
     * 用于中止正在进行的请求
     */
    return () => {
      /**
       * 检查是否存在中止控制器
       * 如果存在则中止请求
       */
      if (abortControllerRef.current) {
        /**
         * 调用abort方法中止请求
         * 防止内存泄漏和竞态条件
         */
        abortControllerRef.current.abort();
      }
    };
  }, [route.path, route.component, cache]);

  /**
   * 条件渲染加载状态组件
   * 当处于加载状态且提供了加载组件时显示
   */
  if (loading && LoadingComponent) {
    /**
     * 渲染自定义加载组件
     * 提供视觉反馈告知用户正在加载
     */
    return <LoadingComponent />;
  }

  /**
   * 条件渲染错误状态组件
   * 当存在错误且提供了错误组件时显示
   */
  if (error && ErrorComponent) {
    /**
     * 渲染自定义错误组件
     * 传递错误对象作为属性
     * 提供错误信息显示
     */
    return <ErrorComponent error={error} />;
  }

  /**
   * 条件渲染已加载路由内容
   * 当路由加载完成时显示
   */
  if (loadedRoute) {
    /**
     * 调用children函数渲染路由内容
     * 传递已加载的路由对象作为参数
     * 使用Fragment包装避免额外的DOM节点
     */
    return <>{children(loadedRoute)}</>;
  }

  /**
   * 默认返回null
   * 当既不处于加载状态也没有错误且未加载完成时显示
   */
  return null;
}
