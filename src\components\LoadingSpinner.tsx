// React 导入，用于构建函数组件
import React from 'react';

/**
 * LoadingSpinnerProps 接口定义了 LoadingSpinner 组件的属性
 *
 * 设计考虑：
 * - size: 可选的尺寸属性，支持三种预定义尺寸
 * - message: 可选的加载消息文本
 * - fullScreen: 可选的全屏显示标识
 * - 所有属性都是可选的，提供默认值以增强组件易用性
 */
interface LoadingSpinnerProps {
  /**
   * 加载动画尺寸
   * 支持三种预定义尺寸：small、medium、large
   * 默认值为 medium
   */
  size?: 'small' | 'medium' | 'large';

  /**
   * 加载消息文本
   * 显示在加载动画下方的提示信息
   * 默认值为 '加载中...'
   */
  message?: string;

  /**
   * 全屏显示标识
   * true 时加载动画居中显示在整个视口中
   * false 时加载动画在容器内居中显示
   * 默认值为 false
   */
  fullScreen?: boolean;
}

/**
 * LoadingSpinner 加载动画组件
 * 提供美观的加载指示器，支持多种尺寸和显示模式
 *
 * 核心功能：
 * 1. 显示旋转的加载动画
 * 2. 支持三种预定义尺寸（small、medium、large）
 * 3. 支持自定义加载消息文本
 * 4. 支持全屏和局部显示模式
 * 5. 响应式设计，适配不同屏幕尺寸
 *
 * 设计决策：
 * 1. 使用函数组件实现，简洁高效
 * 2. 采用CSS-in-JS方式定义样式，便于维护和主题化
 * 3. 使用CSS动画实现平滑的旋转效果
 * 4. 提供灵活的配置选项，满足不同使用场景
 * 5. 使用TypeScript增强类型安全性
 *
 * 用户体验考虑：
 * - 提供视觉反馈，告知用户正在进行加载操作
 * - 支持自定义消息文本，提高信息传达效果
 * - 三种尺寸适应不同布局需求
 * - 全屏模式确保在页面加载时的良好体验
 * - 平滑的动画效果提升视觉感受
 *
 * 性能考虑：
 * - 使用纯CSS动画，避免JavaScript动画的性能开销
 * - 简洁的DOM结构，减少渲染负担
 * - 内联样式减少额外的CSS文件请求
 *
 * 可访问性考虑：
 * - 提供文本提示，便于屏幕阅读器用户理解
 * - 使用高对比度颜色确保可读性
 * - 动画不会引起癫痫等健康问题
 *
 * 扩展性：
 * - 可通过props轻松定制外观和行为
 * - 支持嵌入到任何React应用中
 * - 可作为其他组件的加载状态指示器
 *
 * @param props - LoadingSpinnerProps 属性对象
 * @returns React元素
 */
export default function LoadingSpinner({
  /**
   * 加载动画尺寸属性
   * 可选值：'small' | 'medium' | 'large'
   * 默认值：'medium'
   */
  size = 'medium',

  /**
   * 加载消息文本属性
   * 显示在加载动画下方的提示信息
   * 默认值：'加载中...'
   */
  message = '加载中...',

  /**
   * 全屏显示标识属性
   * true 时加载动画居中显示在整个视口中
   * false 时加载动画在容器内居中显示
   * 默认值：false
   */
  fullScreen = false,
}: LoadingSpinnerProps) {
  /**
   * 尺寸映射对象
   * 将预定义尺寸映射到具体的像素值
   * 便于在样式中使用
   *
   * 设计考虑：
   * - 使用对象映射提高代码可读性
   * - 便于维护和扩展尺寸选项
   * - 确保各尺寸间的视觉层次感
   */
  const sizeMap = {
    /**
     * 小尺寸：20px
     * 适用于紧凑布局或作为内联加载指示器
     */
    small: '20px',

    /**
     * 中等尺寸：40px
     * 默认尺寸，适用于大多数场景
     */
    medium: '40px',

    /**
     * 大尺寸：60px
     * 适用于全屏加载或需要强调的场景
     */
    large: '60px',
  };

  /**
   * 容器样式对象
   * 根据 fullScreen 属性决定布局方式
   *
   * 设计考虑：
   * - 使用条件运算符实现两种布局模式
   * - flex 布局确保内容垂直和水平居中
   * - gap 属性提供元素间的间距
   * - padding 和 minHeight 确保良好的视觉效果
   */
  const containerStyle: React.CSSProperties = fullScreen
    ? {
        /**
         * 全屏模式下的容器样式
         * - 使用 flex 布局实现居中
         * - 最小高度设为视口高度确保全屏效果
         * - gap 提供动画和文本间的间距
         */
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        gap: '1rem',
      }
    : {
        /**
         * 局部模式下的容器样式
         * - 使用 flex 布局实现居中
         * - padding 提供内边距确保视觉效果
         * - gap 提供动画和文本间的间距
         */
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '2rem',
        gap: '1rem',
      };

  /**
   * 加载动画样式对象
   * 创建经典的旋转加载动画效果
   *
   * 设计考虑：
   * - 使用 border 和 borderTop 创建旋转效果
   * - borderRadius 实现圆形外观
   * - animation 应用CSS动画
   * - sizeMap 确保尺寸一致性
   *
   * 视觉效果：
   * - 浅灰色边框作为背景
   * - 蓝色边框作为前景，形成对比
   * - 圆形设计符合现代UI趋势
   */
  const spinnerStyle: React.CSSProperties = {
    /**
     * 宽度和高度根据尺寸属性设置
     * 使用 sizeMap 确保一致性
     */
    width: sizeMap[size],
    height: sizeMap[size],

    /**
     * 边框样式
     * - 浅灰色边框作为背景圆环
     * - 蓝色边框作为前景，形成旋转效果
     */
    border: '3px solid #f3f3f3',
    borderTop: '3px solid #007bff',

    /**
     * 圆形外观
     * 50% borderRadius 创建完美的圆形
     */
    borderRadius: '50%',

    /**
     * 应用旋转动画
     * - spin 动画名称
     * - 1s 持续时间
     * - linear 线性时间函数
     * - infinite 无限循环
     */
    animation: 'spin 1s linear infinite',
  };

  /**
   * 消息文本样式对象
   * 根据尺寸调整字体大小
   *
   * 设计考虑：
   * - 灰色文本确保可读性
   * - 根据尺寸调整字体大小
   * - 与加载动画保持视觉协调
   */
  const messageStyle: React.CSSProperties = {
    /**
     * 文本颜色使用灰色调
     * 确保与背景有良好对比度
     */
    color: '#6c757d',

    /**
     * 根据尺寸调整字体大小
     * 小尺寸使用较小字体，其他尺寸使用标准字体
     */
    fontSize: size === 'small' ? '14px' : '16px',
  };

  /**
   * 渲染加载动画组件
   * 包含容器、加载动画和消息文本
   * 内联定义CSS动画关键帧
   */
  return (
    /**
     * 容器元素
     * 应用 containerStyle 样式
     * 根据 fullScreen 属性决定布局方式
     */
    <div style={containerStyle}>
      <div style={spinnerStyle} />
      {message && <div style={messageStyle}>{message}</div>}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
