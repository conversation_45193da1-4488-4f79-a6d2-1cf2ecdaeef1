import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import '@ant-design/v5-patch-for-react-19';
import { PublicClientApplication } from '@azure/msal-browser';
import { msalConfig } from './utils/authConfig.ts';
import { MsalProvider } from '@azure/msal-react';

const msalInstance = new PublicClientApplication(msalConfig);

async function initializeMsal() {
  try {
    // 1. 初始化 MSAL 实例
    await msalInstance.initialize();
    console.log('MSAL initialized successfully.');

    // 2. 处理重定向 promise (现在 MSAL 已经初始化)
    // 它会处理当前 URL 是否是重定向目标
    const authResult = await msalInstance.handleRedirectPromise();

    if (authResult) {
      console.log('Login successful from redirect:', authResult);
      // 跳转首页
      window.location.href = '/';
    } else {
      console.log('Page loaded, no redirect found or already handled.');
      // 这是页面正常加载，没有经过重定向处理。
      // 如果你想在此处自动登录 (silent)，可以在这里调用 msalInstance.siloAcquireTokenSilent()
      // 但通常 MsalProvider 会自动处理静默获取 token
    }
  } catch (error) {
    console.error('Error initializing MSAL or handling redirect:', error);
    // 在这里处理初始化或重定向的错误
  }
}

// 调用初始化函数
await initializeMsal(); // 注意：这里不需要 await，因为 initializeMsal 是一个异步函数，但渲染不应该被阻塞。

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <MsalProvider instance={msalInstance}>
      <App />
    </MsalProvider>
  </StrictMode>
);
