// React 导入，用于构建组件
import { useState } from 'react';
// 导入路由管理器，用于获取和操作路由
import { routeManager } from '../router/RouteManager';
// 导入创建懒加载路由的函数
import { createLazyRoute } from '../router/routeConfig';
// 导入从JSON创建和注册路由的函数

// 导入JSON路由加载器组件
import JSONRouteLoaderSimple from './JSONRouteLoaderSimple';
// 导入路由定义类型
import type { RouteDefinition } from '../router/types';

/**
 * RouteDebugger 组件用于调试和管理路由配置
 * 提供可视化界面来查看、添加和删除路由，方便开发和调试
 *
 * 核心功能：
 * 1. 显示当前所有路由的列表
 * 2. 提供添加新路由的界面（手动和JSON方式）
 * 3. 支持删除动态添加的路由
 * 4. 以浮动窗口形式显示，不影响主应用布局
 *
 * 设计决策：
 * 1. 使用 useState 管理组件状态（新路由信息、调试器显示状态）
 * 2. 采用浮动窗口设计，通过 showDebugger 状态控制显示/隐藏
 * 3. 提供两种路由添加方式：手动输入和JSON加载
 * 4. 只允许删除动态添加的路由（通过 isDynamic 标识判断）
 *
 * 用户体验考虑：
 * - 提供简洁的浮动按钮来打开调试器
 * - 使用模态窗口设计，确保调试器内容清晰可见
 * - 提供直观的表单和操作按钮
 * - 支持滚动查看长路由列表
 *
 * 安全性考虑：
 * - 对输入进行验证，防止添加无效路由
 * - 限制只能删除动态添加的路由，保护静态路由配置
 *
 * 扩展性：
 * - 通过 JSONRouteLoaderSimple 组件实现JSON路由加载功能
 * - 可轻松添加更多路由管理功能
 */
export default function RouteDebugger() {
  /**
   * 状态变量：新路由路径输入框的值
   * 用于存储用户输入的新路由路径
   */
  const [newRoutePath, setNewRoutePath] = useState('');

  /**
   * 状态变量：新路由名称输入框的值
   * 用于存储用户输入的新路由名称
   */
  const [newRouteName, setNewRouteName] = useState('');

  /**
   * 状态变量：调试器显示状态
   * 控制路由调试器窗口的显示/隐藏
   */
  const [showDebugger, setShowDebugger] = useState(false);

  /**
   * 获取当前所有路由配置
   * 从 routeManager 获取最新的路由列表
   */
  const currentRoutes = routeManager.getRoutes();

  /**
   * 处理添加新路由的函数
   * 验证输入并创建新的路由配置，然后注册到路由管理器
   *
   * 验证规则：
   * - 路径和名称都不能为空
   *
   * 实现细节：
   * - 使用 createLazyRoute 创建懒加载路由
   * - 使用 NotFound 页面作为示例组件
   * - 设置路由元数据（标题、图标、动态标识）
   * - 注册新路由到 routeManager
   * - 清空输入框
   */
  const handleAddRoute = () => {
    // 验证输入，路径和名称不能为空
    if (!newRoutePath || !newRouteName) return;

    /**
     * 创建新的路由定义对象
     * 使用 createLazyRoute 函数创建懒加载路由
     *
     * 参数说明：
     * 1. newRoutePath: 路由路径
     * 2. newRouteName: 路由名称
     * 3. 组件加载函数: 使用动态导入加载 NotFound 组件
     * 4. 路由元数据:
     *    - title: 路由标题，与名称相同
     *    - icon: 路由图标，使用新增图标
     *    - isDynamic: 动态路由标识，标记为 true
     */
    const newRoute: RouteDefinition = createLazyRoute(
      newRoutePath,
      newRouteName,
      () => import('../pages/NotFound'), // 使用404页面作为示例
      {
        title: newRouteName,
        icon: '🆕',
        isDynamic: true,
      }
    );

    // 将新路由注册到路由管理器
    routeManager.register(newRoute);
    // 清空输入框
    setNewRoutePath('');
    setNewRouteName('');
  };

  /**
   * 处理删除路由的函数
   * 从路由管理器中注销指定路径的路由
   *
   * @param path - 要删除的路由路径
   *
   * 设计考虑：
   * - 只能删除动态添加的路由（在UI层面通过按钮显示控制）
   * - 直接调用 routeManager.unregister 方法执行删除操作
   */
  const handleRemoveRoute = (path: string) => {
    routeManager.unregister(path);
  };

  /**
   * 当调试器未显示时，只渲染一个浮动按钮
   * 点击按钮可打开路由调试器窗口
   *
   * 按钮样式特点：
   * - 固定定位在右下角，方便访问
   * - 使用醒目的蓝色背景
   * - 高 zIndex 确保在其他元素之上
   */
  if (!showDebugger) {
    return (
      /**
       * 调试器触发按钮
       * 点击后设置 showDebugger 状态为 true，显示调试器窗口
       */
      <button
        onClick={() => setShowDebugger(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          zIndex: 1000,
        }}
      >
        路由调试器
      </button>
    );
  }

  /**
   * 当调试器显示时，渲染完整的调试器界面
   * 包含路由列表、添加路由表单和JSON路由加载功能
   */
  return (
    /**
     * 调试器主容器
     * 使用固定定位居中显示，确保在视口中央
     * 具有模态窗口样式，包括阴影、圆角和边框
     * 支持滚动以适应长内容
     */
    <div
      style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        backgroundColor: 'white',
        border: '1px solid #ccc',
        borderRadius: '8px',
        padding: '20px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        zIndex: 1000,
        maxWidth: '500px',
        maxHeight: '80vh',
        overflow: 'auto',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
        }}
      >
        <h3>路由调试器</h3>

        <button
          onClick={() => setShowDebugger(false)}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
          }}
        >
          ×
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>添加新路由</h4>
        <div style={{ marginBottom: '15px' }}>
          <h5>手动添加</h5>
          <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
            <input
              type="text"
              placeholder="路由路径 (如: /example)"
              value={newRoutePath}
              onChange={e => setNewRoutePath(e.target.value)}
              style={{
                flex: 1,
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
              }}
            />
            <input
              type="text"
              placeholder="路由名称"
              value={newRouteName}
              onChange={e => setNewRouteName(e.target.value)}
              style={{
                flex: 1,
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
              }}
            />
          </div>
          <button
            onClick={handleAddRoute}
            disabled={!newRoutePath || !newRouteName}
            style={{
              padding: '8px 16px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              width: '100%',
            }}
          ></button>
        </div>
        <div>
          <h5>JSON路由加载</h5>
          <JSONRouteLoaderSimple />
        </div>
      </div>
      <div>
        <h4>当前路由 ({currentRoutes.length})</h4>
        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {currentRoutes.map(route => (
            /**
             * 路由项容器
             * 使用 flex 布局实现两端对齐
             * 底部边框分隔不同路由项
             */
            <div
              key={route.path}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px',
                borderBottom: '1px solid #eee',
              }}
            >
              <div>
                <strong>{route.path}</strong>
                <br />
                <small>{route.name}</small>
                {route.meta?.isDynamic && (
                  <span style={{ color: '#007bff', marginLeft: '5px' }}>
                    [动态]
                  </span>
                )}
              </div>
              {route.meta?.isDynamic && (
                <button
                  onClick={() => handleRemoveRoute(route.path)}
                  style={{
                    padding: '4px 8px',
                    backgroundColor: '#dc3545',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px',
                  }}
                >
                  删除
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
