// React 导入，用于构建类组件
import { Component } from 'react';
// 导入React类型定义
import type { ErrorInfo, ReactNode } from 'react';

/**
 * ErrorBoundaryProps 接口定义了 ErrorBoundary 组件的属性
 *
 * 设计考虑：
 * - children: 必需的子组件，被错误边界保护的组件树
 * - fallback: 可选的错误显示组件，自定义错误界面
 * - onError: 可选的错误处理回调函数，用于记录错误或执行其他操作
 */
interface ErrorBoundaryProps {
  /**
   * 被错误边界保护的子组件
   * 这是必需属性，因为错误边界需要包装其他组件才能发挥作用
   */
  children: ReactNode;

  /**
   * 可选的错误回退组件
   * 当发生错误时显示此组件而不是默认错误界面
   * 允许外部自定义错误显示方式
   */
  fallback?: ReactNode;

  /**
   * 错误处理回调函数
   * 当捕获到错误时调用，可用于记录错误信息或执行其他操作
   *
   * @param error - 捕获到的错误对象
   * @param errorInfo - 包含组件堆栈信息的错误详情
   */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

/**
 * ErrorBoundaryState 接口定义了 ErrorBoundary 组件的状态
 *
 * 状态管理考虑：
 * - hasError: 标识是否发生了错误
 * - error: 存储捕获到的错误对象，用于显示错误信息
 */
interface ErrorBoundaryState {
  /**
   * 错误状态标识
   * true 表示已捕获到错误，false 表示正常状态
   */
  hasError: boolean;

  /**
   * 捕获到的错误对象
   * null 表示没有错误，否则包含错误详情
   */
  error: Error | null;
}

/**
 * ErrorBoundary 错误边界组件
 * 用于捕获和处理子组件树中的JavaScript错误，防止整个应用崩溃
 *
 * 核心功能：
 * 1. 捕获子组件树中的渲染错误
 * 2. 提供友好的错误显示界面
 * 3. 支持自定义错误处理回调
 * 4. 允许自定义错误显示组件
 * 5. 提供重新加载功能
 *
 * 设计决策：
 * 1. 使用类组件实现，因为错误边界需要使用生命周期方法
 * 2. 实现 React 的错误边界标准API（getDerivedStateFromError 和 componentDidCatch）
 * 3. 提供默认错误界面和自定义回退组件两种显示方式
 * 4. 支持错误回调，便于集成外部错误监控系统
 * 5. 提供重新加载按钮，改善用户体验
 *
 * React 错误边界机制：
 * - getDerivedStateFromError: 静态方法，在渲染阶段捕获错误并更新状态
 * - componentDidCatch: 实例方法，在提交阶段记录错误信息
 *
 * 用户体验考虑：
 * - 提供清晰的错误信息显示
 * - 使用友好的视觉设计（颜色、间距、圆角）
 * - 提供重新加载按钮，允许用户恢复应用
 * - 支持自定义错误界面，满足不同应用场景需求
 *
 * 安全性考虑：
 * - 不会捕获事件处理器中的错误（这是设计特性）
 * - 不会捕获异步代码中的错误（如setTimeout、Promise）
 * - 不会捕获服务端渲染期间的错误
 * - 不会捕获边界自身抛出的错误
 *
 * 扩展性：
 * - 通过 fallback 属性支持自定义错误界面
 * - 通过 onError 回调支持外部错误处理
 * - 可以嵌套使用多个错误边界实现精细化错误处理
 */
export default class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  /**
   * 构造函数
   * 初始化组件状态
   *
   * @param props - 组件属性
   */
  constructor(props: ErrorBoundaryProps) {
    // 调用父类构造函数
    super(props);
    // 初始化状态
    this.state = {
      /**
       * 错误状态初始化为false
       * 表示组件初始处于正常状态
       */
      hasError: false,

      /**
       * 错误对象初始化为null
       * 表示尚未捕获到任何错误
       */
      error: null,
    };
  }

  /**
   * 静态方法：getDerivedStateFromError
   * 在渲染阶段捕获错误并更新状态
   * 这是React错误边界的核心API之一
   *
   * 执行时机：
   * - 在子组件渲染过程中抛出错误时调用
   * - 在子组件生命周期方法中抛出错误时调用
   * - 在子组件构造函数中抛出错误时调用
   *
   * @param error - 捕获到的错误对象
   * @returns 新的状态对象，用于更新组件状态
   *
   * 设计考虑：
   * - 必须是静态方法，无法访问组件实例
   * - 应该只用于更新状态，不应用于副作用操作
   * - 返回的状态将与当前状态合并
   */
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 返回新的状态对象，标记已发生错误并保存错误信息
    return {
      /**
       * 设置错误状态为true
       * 触发组件重新渲染并显示错误界面
       */
      hasError: true,

      /**
       * 保存捕获到的错误对象
       * 用于在错误界面中显示错误信息
       */
      error,
    };
  }

  /**
   * 实例方法：componentDidCatch
   * 在提交阶段记录错误信息
   * 这是React错误边界的另一个核心API
   *
   * 执行时机：
   * - 在错误被getDerivedStateFromError处理后调用
   * - 在组件更新提交到DOM后执行
   *
   * @param error - 捕获到的错误对象
   * @param errorInfo - 包含组件堆栈信息的错误详情
   *
   * 设计考虑：
   * - 用于执行副作用操作，如记录错误日志
   * - 可以访问组件实例，能够调用props方法
   * - 不应该用于更新状态（应使用getDerivedStateFromError）
   *
   * 使用场景：
   * - 发送错误报告到监控服务
   * - 记录错误日志到控制台
   * - 更新应用状态以反映错误情况
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    /**
     * 在控制台输出错误信息
     * 便于开发时调试和定位问题
     * 包含错误对象和堆栈信息
     */
    console.error('Error caught by boundary:', error, errorInfo);

    /**
     * 如果提供了错误处理回调函数，则调用它
     * 允许外部组件处理错误，如发送到监控服务
     */
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  /**
   * 渲染方法
   * 根据错误状态决定渲染正常内容还是错误界面
   *
   * 渲染逻辑：
   * 1. 如果有错误且提供了自定义回退组件，则渲染回退组件
   * 2. 如果有错误但没有提供回退组件，则渲染默认错误界面
   * 3. 如果没有错误，则渲染子组件
   *
   * @returns React元素
   */
  render() {
    /**
     * 检查是否发生了错误
     * 如果hasError为true，则显示错误界面
     */
    if (this.state.hasError) {
      /**
       * 检查是否提供了自定义回退组件
       * 如果提供了，则渲染自定义错误界面
       */
      if (this.props.fallback) {
        return this.props.fallback;
      }

      /**
       * 渲染默认错误界面
       * 包含错误信息显示和重新加载按钮
       * 使用内联样式确保在任何环境下都能正常显示
       */
      return (
        /**
         * 错误界面容器
         * 使用flex布局居中显示内容
         * 设置最小高度确保内容可见
         * 使用浅灰色背景和圆角提升视觉效果
         */
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '200px',
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            margin: '1rem',
          }}
        >
          /** * 错误标题 * 使用红色文本突出显示错误状态 *
          设置底部边距分隔标题和内容 */
          <h3 style={{ color: '#dc3545', marginBottom: '1rem' }}>
            页面加载失败
          </h3>
          /** * 错误详情信息 * 显示具体的错误消息 * 使用灰色文本和底部边距 *
          如果没有错误消息则显示默认文本 */
          <p style={{ color: '#6c757d', marginBottom: '1rem' }}>
            {this.state.error?.message || '加载页面时发生未知错误'}
          </p>
          /** * 重新加载按钮 * 点击后重新加载当前页面 * 使用蓝色背景和白色文本 *
          设置悬停效果提升用户体验 */
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            重新加载
          </button>
        </div>
      );
    }

    /**
     * 如果没有发生错误，则渲染子组件
     * 这是错误边界正常情况下的行为
     */
    return this.props.children;
  }
}
