# 客户端路由集成文档

## 概述

本文档详细记录了在现有 React + Vite + TypeScript 单页应用中集成客户端路由的完整过程。该实现确保所有导航操作都在客户端完成，无需重新加载页面，同时保持服务端对所有路由请求返回相同的 `index.html`。

## 功能特性

- **纯客户端路由** - 所有页面切换都在浏览器端完成
- **无SSR依赖** - 明确禁用服务端渲染
- **历史API支持** - 支持浏览器前进/后退按钮
- **404处理** - 优雅处理不存在的路由
- **响应式导航** - 自适应导航组件
- **TypeScript支持** - 完整的类型定义

## 技术栈

- **React Router v6** - 现代路由解决方案
- **React 19** - 最新React版本
- **Vite** - 构建工具（已配置SPA回退）
- **TypeScript** - 类型安全

## 安装与配置

### 1. 依赖安装

```bash
cnpm install react-router-dom
```

### 2. 项目结构

```
src/
├── pages/
│   ├── Home.tsx          # 首页 - 保留原有计数器功能
│   ├── About.tsx         # 关于页面
│   ├── Contact.tsx       # 联系页面
│   └── NotFound.tsx      # 404错误页面
├── components/
│   └── Navigation.tsx    # 导航组件
├── App.tsx               # 路由配置中心
└── main.tsx              # 应用入口
```

## 路由配置

### 路由映射表

| 路径       | 组件       | 描述               |
| ---------- | ---------- | ------------------ |
| `/`        | `Home`     | 首页，保留原有功能 |
| `/about`   | `About`    | 关于页面           |
| `/contact` | `Contact`  | 联系页面           |
| `*`        | `NotFound` | 404错误处理        |

### 路由实现

```typescript
// src/App.tsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'

<Router>
  <Routes>
    <Route path="/" element={<Home />} />
    <Route path="/about" element={<About />} />
    <Route path="/contact" element={<Contact />} />
    <Route path="*" element={<NotFound />} />
  </Routes>
</Router>
```

## 组件API

### Navigation组件

**位置**: `src/components/Navigation.tsx`

**功能**: 提供响应式导航菜单，支持路由高亮

**Props**: 无（使用React Router的useLocation钩子）

**使用示例**:

```typescript
import Navigation from './components/Navigation'

function App() {
  return (
    <Router>
      <Navigation />
      {/* 其他内容 */}
    </Router>
  )
}
```

### 页面组件

#### Home组件

- **路径**: `src/pages/Home.tsx`
- **功能**: 保留原有计数器功能，作为应用首页
- **路由**: `/`

#### About组件

- **路径**: `src/pages/About.tsx`
- **功能**: 展示应用信息和技术栈
- **路由**: `/about`

#### Contact组件

- **路径**: `src/pages/Contact.tsx`
- **功能**: 联系表单和信息展示
- **路由**: `/contact`

#### NotFound组件

- **路径**: `src/pages/NotFound.tsx`
- **功能**: 404错误页面，提供返回首页链接
- **路由**: `*`（所有未匹配路由）

## 配置详情

### Vite配置

Vite开发服务器已默认支持SPA路由回退，无需额外配置。所有路由请求都会返回 `index.html`，确保客户端路由正常工作。

### 生产环境部署

对于生产环境，需要配置Web服务器：

**Nginx配置示例**:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

**Apache配置示例**:

```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>
```

## 使用指南

### 基本导航

```typescript
// 声明式导航
import { Link } from 'react-router-dom'
<Link to="/about">关于我们</Link>

// 程序化导航
import { useNavigate } from 'react-router-dom'
const navigate = useNavigate()
navigate('/contact')
```

### 获取当前路由

```typescript
import { useLocation } from 'react-router-dom';
const location = useLocation();
console.log(location.pathname); // 当前路径
```

### 路由参数

```typescript
// 定义带参数的路由
<Route path="/user/:id" element={<User />} />

// 在组件中获取参数
import { useParams } from 'react-router-dom'
const { id } = useParams()
```

## 迁移指南

### 从传统多页应用迁移

1. **移除页面刷新** - 所有 `<a href="...">` 替换为 `<Link to="...">`
2. **状态管理** - 使用React状态管理替代URL参数
3. **SEO考虑** - 如需SEO，考虑预渲染方案

### 从旧版React Router迁移

1. **Switch → Routes** - 将 `<Switch>` 替换为 `<Routes>`
2. **Component → element** - 将 `component={Component}` 替换为 `element={<Component />}`
3. **exact属性** - v6中不再需要 `exact` 属性

## 最佳实践

### 代码组织

- 将路由配置集中在 `App.tsx`
- 页面组件放在 `pages/` 目录
- 共享组件放在 `components/` 目录
- 使用懒加载优化大型应用

### 性能优化

```typescript
// 路由懒加载
const LazyComponent = React.lazy(() => import('./pages/LazyPage'))

<Route path="/lazy" element={
  <React.Suspense fallback={<div>Loading...</div>}>
    <LazyComponent />
  </React.Suspense>
} />
```

### 错误处理

- 始终定义404路由
- 使用错误边界处理组件错误
- 提供友好的错误页面

## 故障排除

### 常见问题

**问题**: 直接访问路由返回404
**解决**: 确保Web服务器配置了回退规则

**问题**: 路由不匹配
**解决**: 检查路由路径定义和访问路径是否一致

**问题**: 导航不更新
**解决**: 确保使用 `Link` 组件而非原生 `<a>` 标签

## 扩展功能

### 嵌套路由

```typescript
<Route path="/dashboard" element={<Dashboard />}>
  <Route path="settings" element={<Settings />} />
  <Route path="profile" element={<Profile />} />
</Route>
```

### 路由守卫

```typescript
const ProtectedRoute = ({ children }) => {
  const isAuthenticated = useAuth()
  return isAuthenticated ? children : <Navigate to="/login" />
}
```

## 版本信息

- **React Router**: v7.7.1
- **React**: v19.1.0
- **Vite**: v7.0.4
- **TypeScript**: ~5.8.3

## 相关文档

- [React Router官方文档](https://reactrouter.com/)
- [Vite配置指南](https://vitejs.dev/config/)
- [React文档](https://react.dev/)
