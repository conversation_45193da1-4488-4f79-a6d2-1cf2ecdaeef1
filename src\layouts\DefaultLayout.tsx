import { Layout } from 'antd';
import React from 'react';

interface DefaultLayoutProps {
  children: React.ReactNode;
}

/**
 * 默认布局组件
 * 用于常规页面，包含标准导航和页脚
 */
export default function DefaultLayout({ children }: DefaultLayoutProps) {
  return (
    <Layout style={{ height: '100vh' }}>
      <Layout.Header style={{ backgroundColor: '#0d759f', padding: '0' }}>
        <div style={{ height: '60px' }}></div>
        <div
          style={{
            backgroundImage: 'linear-gradient(270deg,#45A2CD 0%,#9AC258 100%)',
            height: '4px',
          }}
        ></div>
      </Layout.Header>
      <Layout>
        <Layout.Sider
          theme="light"
          style={{ background: 'rgb(0, 21, 41)' }}
        ></Layout.Sider>
        <Layout.Content>{children}</Layout.Content>
      </Layout>
    </Layout>
  );
}
