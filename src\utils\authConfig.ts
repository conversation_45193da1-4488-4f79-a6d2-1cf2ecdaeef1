import { LogLevel } from '@azure/msal-browser';

/**
 * Azure AD B2C 配置
 */
export const msalConfig = {
  auth: {
    clientId: '22ee9be1-28b3-43c9-bfbc-772302da630d',
    authority:
      'https://login.microsoftonline.com/c8911b39-02ac-439e-8f9d-11f78a43857a',
    redirectUri: 'http://localhost:5173/login',
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level: LogLevel, message: any, containsPii: any) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case 0: // Error
            console.error(message);
            return;
          case 1: // Warning
            console.warn(message);
            return;
          case 2: // Info
            return;
          case 3: // Verbose
            console.debug(message);
            return;
        }
      },
    },
  },
};

/**
 * 配置 MSAL
 */
export const loginRequest = {
  scopes: ['User.Read'],
};

export const graphConfig = {
  graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me',
};
