/**
 * 认证工具函数
 * 提供用户认证相关的工具函数，包括token管理、用户信息存储等
 */

import type {
  AuthUser,
  AuthToken,
  AuthCredentials,
  LoginResponse,
} from '../types/auth';

// 存储键名常量 .env + 存储键名
const STORAGE_KEYS = {
  TOKEN: import.meta.env.VITE_PREFIX + 'auth_token',
  REFRESH_TOKEN: import.meta.env.VITE_PREFIX + 'auth_refresh_token',
  USER: import.meta.env.VITE_PREFIX + 'auth_user',
} as const;


/**
 * 根据本地 Token , 校验登录状态
 * @returns true OR false
 */
export function isLocalAuthenticated(): boolean {
  // 获取本地存储的 Token
  const token = getToken();
  // 如果 Token 存在，则认为用户已认证
  return !!token;
}


/**
 * 获取存储的token
 */
export const getToken = (): string | null => {
  try {
    const tokenStr = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (!tokenStr) return null;

    const token = parseJwt(tokenStr);
    if (!isValidToken(token)) return null;
    return tokenStr;
  } catch (error) {
    console.error('Error parsing token from localStorage:', error);
    return null;
  }
};

/**
 * 验证 token
 * @param token 
 * @returns 
 */
export const isValidToken = (token: AuthToken): boolean => {
  if (!token || !token.userId) return false;
  // 检查token是否已过期
  if (token.expiresAt && token.expiresAt < Date.now()) return false;
  return true;
};

/**
 * 设置token到localStorage
 */
export const setToken = (token: string): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
  } catch (error) {
    console.error('Error saving token to localStorage:', error);
  }
};

export const setRefreshToken = (refreshToken: string): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  } catch (error) {
    console.error('Error saving refresh token to localStorage:', error);
  }
};

/**
 * 获取当前用户信息
 */
export const getCurrentUser = (): AuthUser | null => {
  try {
    const userStr = localStorage.getItem(STORAGE_KEYS.USER);
    if (!userStr) return null;

    const user = JSON.parse(userStr);
    return user;
  } catch (error) {
    console.error('Error parsing user from localStorage:', error);
    return null;
  }
};

/**
 * 设置当前用户信息
 */
export const setCurrentUser = (user: AuthUser): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
  } catch (error) {
    console.error('Error saving user to localStorage:', error);
  }
};

/**
 * 清除所有认证信息
 */
export const clearAuth = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};



/**
 * 获取重定向路径
 */
export const getRedirectPath = (): string => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('redirect') || '/';
};

/**
 * 构建登录URL（包含重定向参数）
 */
export const buildLoginUrl = (currentPath: string): string => {
  return `/login?redirect=${encodeURIComponent(currentPath)}`;
};


/**
 * 将 jwt Token 解析
 */
export const parseJwt = (token: string): AuthToken => {

  return JSON.parse(atob(token.split('.')[1]));

};


export const logout = (): void => {
  clearAuth();
};