import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { User } from '../types/user';
import type { LayoutType } from '../router/types';

interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  userMenus: UserMenu[];

}

interface UserMenu {
  path: string;
  component: string;
  name?: string;
  exact?: boolean;
  layout?: LayoutType;
  meta?: Record<string, any>;
}

type UserAction = {
  setUser: (user: User | null) => void;
  clearUser: () => void;
  setUserMenus: (menus: UserMenu[]) => void;
}

type UserStore = UserState & UserAction;

export const useUserStore = create<UserStore>()(
  persist(
    set => ({
      user: null,
      isAuthenticated: false,
      userMenus: [],
      setUser: (user: User | null) => set(() => ({ user, isAuthenticated: user !== null })),
      clearUser: () => set(() => ({ user: null, isAuthenticated: false, userMenus: [] })),
      setUserMenus: (menus) => set(() => ({ userMenus: menus })),
    }),
    {
      name: 'user-storage', // localStorage中存储的键名
      storage: createJSONStorage(() => localStorage), // 使用localStorage存储
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        userMenus: state.userMenus
      }), // 指定需要持久化的状态
    }
  )
);