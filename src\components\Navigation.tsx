// React Router DOM 导入
// Link 组件用于创建导航链接
// useLocation 钩子用于获取当前路径信息
import { Link, useLocation } from 'react-router-dom';

/**
 * Navigation 导航组件
 * 提供静态导航菜单，包含预定义的导航项
 *
 * 核心功能：
 * 1. 显示水平导航菜单
 * 2. 根据当前路径高亮活动链接
 * 3. 使用React Router实现客户端路由导航
 * 4. 响应式设计，适配不同屏幕尺寸
 *
 * 设计决策：
 * 1. 使用函数组件实现，简洁高效
 * 2. 采用CSS-in-JS方式定义样式，便于维护和主题化
 * 3. 使用useLocation钩子获取当前路径，实现活动链接高亮
 * 4. 预定义导航项数组，便于维护和扩展
 * 5. 使用语义化HTML结构（nav、ul、li）
 *
 * 与DynamicNavigation的区别：
 * 1. 静态导航项，不支持动态添加/删除
 * 2. 导航项硬编码在组件中
 * 3. 不依赖路由管理器
 * 4. 更轻量级，适合简单应用
 *
 * 用户体验考虑：
 * - 提供视觉反馈指示当前活动页面
 * - 使用平滑的过渡动画提升交互感受
 * - 合理的间距和颜色确保可读性
 * - 悬停效果增强交互性
 *
 * 可访问性考虑：
 * - 使用语义化HTML标签
 * - 提供足够的颜色对比度
 * - 保持一致的导航结构
 *
 * 扩展性：
 * - 可通过修改navItems数组添加更多导航项
 * - 样式可通过props或CSS类名进行定制
 * - 可轻松集成到任何React Router应用中
 *
 * @returns React元素
 */
function Navigation() {
  /**
   * 使用useLocation钩子获取当前路径信息
   * 用于确定哪个导航项应该是活动状态
   * location.pathname包含当前URL路径
   */
  const location = useLocation();

  /**
   * 导航项数组
   * 包含所有导航链接的路径和标签
   *
   * 数据结构：
   * - path: 导航链接的目标路径
   * - label: 导航项显示的文本标签
   *
   * 设计考虑：
   * - 使用数组便于遍历和渲染
   * - 对象结构清晰，易于理解和维护
   * - 可以轻松添加更多导航项
   */
  const navItems = [
    /**
     * 首页导航项
     * 路径：/
     * 标签：首页
     */
    { path: '/', label: '首页' },

    /**
     * 关于页面导航项
     * 路径：/about
     * 标签：关于
     */
    { path: '/about', label: '关于' },

    /**
     * 联系页面导航项
     * 路径：/contact
     * 标签：联系
     */
    { path: '/contact', label: '联系' },
  ];

  /**
   * 渲染导航组件
   * 包含导航容器、列表和链接项
   */
  return (
    /**
     * 导航容器元素
     * 使用语义化的nav标签
     * 应用背景色、内边距和边框样式
     *
     * 样式特点：
     * - 浅灰色背景提供视觉区分
     * - 底部边框增强层次感
     * - 适当的内边距和外边距确保间距
     */
    <nav
      style={{
        /**
         * 背景色设置为浅灰色
         * 与页面背景形成对比
         */
        backgroundColor: '#f8f9fa',

        /**
         * 内边距提供内容与边框的间距
         */
        padding: '1rem',

        /**
         * 底部外边距确保与其他内容的间距
         */
        marginBottom: '2rem',

        /**
         * 底部边框增强视觉层次
         */
        borderBottom: '1px solid #dee2e6',
      }}
    >
      /** * 导航列表容器 * 使用无序列表组织导航项 * 应用flex布局实现水平排列 * *
      样式特点： * - flex布局实现水平排列 * - 居中对齐提升视觉效果 * -
      无项目符号保持简洁外观 * - 适当的间距确保导航项之间有足够空间 */
      <ul
        style={{
          /**
           * 使用flex布局实现水平排列
           */
          display: 'flex',

          /**
           * 导航项之间的间距
           */
          gap: '2rem',

          /**
           * 移除默认的项目符号
           */
          listStyle: 'none',

          /**
           * 移除默认的外边距
           */
          margin: 0,

          /**
           * 移除默认的内边距
           */
          padding: '0',

          /**
           * 居中对齐导航项
           */
          justifyContent: 'center',
        }}
      >
        /** * 遍历导航项数组，为每个项创建导航链接 *
        使用path作为key确保列表项的唯一性 */
        {navItems.map(item => (
          /**
           * 导航项列表元素
           * 包含单个导航链接
           */
          <li key={item.path}>
            /** * React Router的Link组件 * 创建客户端导航链接，避免页面刷新 *
            根据当前路径动态应用样式 * * 样式特点： * - 无下划线保持简洁外观 * -
            活动链接使用蓝色文本和加粗字体 * - 活动链接具有浅蓝色背景 * -
            圆角和内边距提升视觉效果 * - 悬停和过渡效果增强交互体验 */
            <Link
              /**
               * 链接目标路径
               * 从导航项对象中获取
               */
              to={item.path}
              /**
               * 链接样式对象
               * 根据当前路径动态调整样式
               */
              style={{
                /**
                 * 移除默认下划线
                 */
                textDecoration: 'none',

                /**
                 * 文本颜色
                 * 活动链接使用蓝色，非活动链接使用灰色
                 */
                color: location.pathname === item.path ? '#007bff' : '#495057',

                /**
                 * 字体粗细
                 * 活动链接加粗，非活动链接正常
                 */
                fontWeight: location.pathname === item.path ? 'bold' : 'normal',

                /**
                 * 内边距提供内容与边框的间距
                 */
                padding: '0.5rem 1rem',

                /**
                 * 圆角提升视觉效果
                 */
                borderRadius: '4px',

                /**
                 * 过渡动画
                 * 所有属性变化在0.3秒内平滑过渡
                 */
                transition: 'all 0.3s ease',

                /**
                 * 背景色
                 * 活动链接使用浅蓝色背景，非活动链接透明
                 */
                backgroundColor:
                  location.pathname === item.path ? '#e7f3ff' : 'transparent',
              }}
            >
              /** * 导航项标签文本 * 从导航项对象中获取 */
              {item.label}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
}

/**
 * 导出Navigation组件
 * 使其可以在其他模块中导入和使用
 */
export default Navigation;
