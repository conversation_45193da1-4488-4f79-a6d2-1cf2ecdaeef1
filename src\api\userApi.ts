import type { LayoutType } from "../router/types";
import apiClient from "../utils/request/axiosClient";


export const getUserMenus = async (userId: string) => {
    return await apiClient.get<{
        path: string;
        component: string;
        name?: string;
        exact?: boolean;
        layout?: LayoutType;
        meta?: Record<string, any>;
    }[]>(`/user/user-menu-list/${userId}`);

};