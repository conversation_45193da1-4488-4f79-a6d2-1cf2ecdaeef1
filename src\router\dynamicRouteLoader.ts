// 导入路由定义类型
import type { RouteDefinition, LayoutType } from './types';
// 导入React组件类型
import type { ComponentType } from 'react';

/**
 * 使用 Vite 的 import.meta.glob 动态导入所有页面组件
 *
 * 设计决策：
 * 1. 利用 Vite 的模块自动化功能，无需手动维护组件列表
 * 2. 使用动态导入实现代码分割，提高应用性能
 * 3. 通过通配符匹配所有 .tsx 文件，自动包含新添加的页面组件
 *
 * 性能考虑：
 * - 只在构建时解析 glob 模式，运行时无性能影响
 * - 动态导入确保组件按需加载
 *
 * 扩展性：
 * - 新增页面组件时无需修改此文件
 * - 自动包含所有符合命名规则的组件
 */
const pageModules = import.meta.glob('../pages/**/*.tsx');

/**
 * 组件映射表
 * 用于存储组件名称到动态导入函数的映射关系
 *
 * 数据结构选择理由：
 * - 使用 Map 而不是普通对象，提供更好的性能和API
 * - 键为组件名称，值为返回组件模块的异步函数
 *
 * 设计考虑：
 * - 便于通过组件名称快速查找对应的导入函数
 * - 支持动态路由加载时的组件解析
 */
const componentMap = new Map<
  string,
  () => Promise<{ default: ComponentType }>
>();

/**
 * 初始化组件映射表
 * 遍历 pageModules 对象，提取组件名称并建立映射关系
 *
 * 处理流程：
 * 1. 遍历所有导入的模块路径和对应的导入函数
 * 2. 从文件路径中提取组件名称（去除目录和扩展名）
 * 3. 建立组件名称到导入函数的映射
 *
 * 错误处理：
 * - 过滤无法提取组件名称的路径
 * - 确保只有有效组件被添加到映射表中
 */
Object.entries(pageModules).forEach(([path, importFn]) => {
  /**
   * 从文件路径中提取组件名称
   * 例如: "../pages/Home.tsx" -> "Home"
   *
   * 实现步骤：
   * 1. 按 '/' 分割路径获取文件名部分
   * 2. 移除 '.tsx' 扩展名
   * 3. 确保组件名称有效
   */
  const componentName = path?.replace('../', '')?.replace('.tsx', '');

  /**
   * 如果成功提取组件名称，则添加到映射表中
   * 类型断言确保导入函数符合预期签名
   */
  if (componentName) {
    componentMap.set(
      componentName,
      importFn as () => Promise<{ default: ComponentType }>
    );
  }
});

/**
 * NotFound 组件的默认导入函数
 * 当无法找到指定组件时使用此默认组件
 *
 * 设计考虑：
 * - 提供容错机制，防止因组件缺失导致应用崩溃
 * - 返回一个空组件作为占位符
 * - 确保路由系统始终有可用的组件
 */
const NotFoundComponent =
  componentMap.get('NotFound') ||
  (() => Promise.resolve({ default: () => null }));

/**
 * 从JSON路由配置创建懒加载路由定义数组
 *
 * 功能说明：
 * 1. 将JSON格式的路由配置转换为内部路由定义格式
 * 2. 解析组件名称并关联对应的动态导入函数
 * 3. 设置默认值和元数据
 *
 * @param jsonRoutes - JSON格式的路由配置数组
 *   每个路由对象包含:
 *   - path: 路由路径（必需）
 *   - component: 组件名称（必需）
 *   - name: 路由名称（可选，默认为组件名称）
 *   - exact: 精确匹配标识（可选，默认为false）
 *   - layout: 布局类型（可选，默认为'default'）
 *   - meta: 路由元数据（可选）
 *
 * @returns 路由定义对象数组，符合 RouteDefinition 类型
 *
 * 实现细节：
 * - 为每个路由查找对应的组件导入函数
 * - 无法找到组件时使用 NotFoundComponent 作为后备
 * - 添加动态路由标识到元数据中
 * - 保留原始组件名称供调试使用
 *
 * 性能考虑：
 * - 使用组件映射表实现O(1)查找时间
 * - 保持函数纯度，便于测试和缓存
 */
export function createRoutesFromJSON(
  jsonRoutes: Array<{
    path: string;
    component: string;
    name?: string;
    exact?: boolean;
    layout?: LayoutType;
    meta?: Record<string, any>;
  }>
): RouteDefinition[] {
  // 调试日志，输出组件映射表内容
  console.log('componentMap', componentMap);

  /**
   * 遍历JSON路由配置数组，为每个路由创建路由定义对象
   */
  return jsonRoutes.map(jsonRoute => {
    /**
     * 获取路由配置中的组件名称
     */
    const componentName = jsonRoute.component;

    /**
     * 查找对应的组件导入函数
     * 如果找不到则使用 NotFoundComponent 作为后备
     */
    const componentLoader =
      componentMap.get(componentName) || NotFoundComponent;

    // 调试日志，输出组件加载器
    console.log('componentName', componentLoader);

    /**
     * 构建路由定义对象
     * 包含路径、组件加载器、名称、精确匹配标识和元数据
     */
    return {
      /**
       * 路由路径，直接使用JSON配置中的值
       */
      path: jsonRoute.path,

      /**
       * 组件加载函数，用于懒加载对应组件
       */
      component: componentLoader,

      /**
       * 路由名称，如果未提供则使用组件名称
       */
      name: jsonRoute.name || componentName,

      /**
       * 精确匹配标识，如果未提供则默认为false
       */
      exact: jsonRoute.exact ?? false,

      /**
       * 布局类型，如果未提供则默认为'default'
       */
      layout: jsonRoute.layout || 'default',

      /**
       * 路由元数据，包含原始配置和额外信息
       */
      meta: {
        /**
         * 保留原始JSON配置中的元数据
         */
        ...jsonRoute.meta,

        /**
         * 添加原始组件名称，便于调试和追踪
         */
        originalComponent: componentName,

        /**
         * 标记为动态路由，用于区分静态路由
         */
        isDynamic: true,
      },
    };
  });
}

/**
 * 动态注册JSON路由配置
 *
 * 功能说明：
 * 1. 异步导入路由管理器
 * 2. 将JSON路由配置转换为路由定义
 * 3. 注册到路由管理器中使其生效
 *
 * 设计决策：
 * - 异步导入 LazyRouter 避免循环依赖
 * - 批量注册路由提高效率
 * - 使用 createRoutesFromJSON 复用转换逻辑
 *
 * @param jsonRoutes - JSON格式的路由配置数组
 *
 * 实现细节：
 * - 动态导入 LazyRouter 获取 routeManager 实例
 * - 调用 createRoutesFromJSON 转换路由配置
 * - 遍历路由定义数组并逐个注册
 *
 * 错误处理：
 * - 依赖 createRoutesFromJSON 的容错机制
 * - 路由管理器应处理重复注册等边界情况
 */
export async function registerJSONRoutes(
  jsonRoutes: Array<{
    path: string;
    component: string;
    name?: string;
    exact?: boolean;
    layout?: LayoutType;
    meta?: Record<string, any>;
  }>
) {
  /**
   * 异步导入 LazyRouter 模块获取 routeManager
   * 避免模块间的循环依赖问题
   */
  const { routeManager } = await import('./LazyRouter');

  /**
   * 将JSON路由配置转换为路由定义数组
   */
  const routes = createRoutesFromJSON(jsonRoutes);

  /**
   * 遍历路由定义数组，逐个注册到路由管理器
   */
  routes.forEach(route => routeManager.register(route));
}

/**
 * 获取所有可用组件的名称列表
 *
 * 功能说明：
 * 1. 从组件映射表中提取所有键（组件名称）
 * 2. 转换为数组格式返回
 *
 * 使用场景：
 * - 为UI组件提供组件选择列表
 * - 调试和开发时查看可用组件
 * - 验证组件名称有效性
 *
 * @returns 组件名称字符串数组
 *
 * 实现细节：
 * - 使用 Array.from 将 Map 的键转换为数组
 * - 保持与组件映射表的同步更新
 */
export function getAvailableComponents(): string[] {
  return Array.from(componentMap.keys());
}

/**
 * 检查指定名称的组件是否存在
 *
 * 功能说明：
 * 1. 在组件映射表中查找指定组件名称
 * 2. 返回布尔值表示是否存在
 *
 * 使用场景：
 * - 验证路由配置中的组件名称
 * - 条件渲染基于组件可用性的UI元素
 * - 路由解析时的前置检查
 *
 * @param componentName - 要检查的组件名称
 * @returns 如果组件存在返回true，否则返回false
 *
 * 实现细节：
 * - 利用 Map.has 方法实现O(1)时间复杂度查找
 * - 不区分大小写的比较（当前实现区分大小写）
 */
export function hasComponent(componentName: string): boolean {
  return componentMap.has(componentName);
}

/**
 * 导出组件映射表供外部使用和调试
 *
 * 导出目的：
 * 1. 便于调试和开发时查看组件映射关系
 * 2. 允许其他模块直接访问组件映射表
 * 3. 支持高级用例中的自定义组件解析
 *
 * 注意事项：
 * - 导出的映射表是只读的，避免外部意外修改
 * - 在生产环境中可能需要条件导出
 */
export { componentMap };
