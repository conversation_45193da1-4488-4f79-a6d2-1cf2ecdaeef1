# React + TypeScript + Vite 模板项目

这是一个功能丰富的 React 模板项目，集成了现代前端开发所需的各种工具和最佳实践。

## 🌟 特性

- **React 19** - 使用最新版本的 React
- **TypeScript** - 强类型检查，提高代码质量
- **Vite 7** - 极速的构建工具
- **ESLint + Prettier** - 代码规范和格式化
- **React Router v7** - 客户端路由
- **TanStack Query** - 服务器状态管理
- **SWR** - 数据获取库
- **Zustand** - 客户端状态管理
- **Ant Design** - UI 组件库
- **MSAL** - Microsoft 身份验证

## 🚀 快速开始

### 克隆项目

```bash
git clone <repository-url>
cd react-template
```

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发模式

```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:5173 查看应用。

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 预览生产构建

```bash
npm run preview
# 或
yarn preview
```

## 📁 项目结构

```
src/
├── components/           # 通用组件
├── layouts/              # 页面布局
├── pages/                # 页面组件
├── router/               # 路由系统
├── types/                # 类型定义
├── utils/                # 工具函数
├── App.tsx               # 根组件
└── main.tsx              # 入口文件
```

## 🛠️ 开发工具

### 代码检查

```bash
npm run lint
# 或修复可自动修复的问题
npm run lint:fix
```

### 代码格式化

```bash
npm run format
```

### 类型检查

```bash
npx tsc --noEmit
```

### 综合检查

```bash
npm run check
# 或自动修复并检查
npm run check:fix
```

## 📚 文档

详细文档请查看 [docs](./docs) 目录：

- [路由系统文档](./docs/README.md) - 详细介绍路由系统功能和使用方法
- [认证实现计划](./docs/auth-implementation-plan.md) - 认证系统的实现规划
- [客户端路由集成](./docs/client-side-routing.md) - 客户端路由的集成过程

## 🧪 技术栈详情

| 技术 | 版本 | 用途 |
|------|------|------|
| React | 19.1.0 | UI 框架 |
| TypeScript | ~5.8.3 | 类型检查 |
| Vite | ^7.0.4 | 构建工具 |
| React Router | ^7.7.1 | 路由管理 |
| Ant Design | ^5.26.6 | UI 组件库 |
| TanStack Query | ^5.83.0 | 服务器状态管理 |
| SWR | ^2.3.4 | 数据获取 |
| Zustand | ^5.0.6 | 客户端状态管理 |
| MSAL | ^4.16.0 | Microsoft 身份验证 |

## 🎯 核心功能模块

### 路由系统

项目包含一个功能完整的动态路由系统，支持：
- 组件懒加载
- 运行时路由注册
- 路由缓存
- JSON 配置路由
- 多种布局支持

### 认证系统

集成 Microsoft 身份验证库 (MSAL)，支持：
- Azure AD 认证
- 路由守卫
- Token 管理

### 状态管理

提供多种状态管理方案：
- TanStack Query 用于服务器状态
- SWR 作为备选数据获取方案
- Zustand 用于客户端状态

## 📦 环境配置

通过 `.env` 文件配置环境变量：

```env
VITE_API_URL=https://api.example.com
VITE_APP_TITLE=My App
```

## 🚢 部署

构建项目：

```bash
npm run build
```

构建产物位于 `dist` 目录，可部署到任何静态文件服务器。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个模板项目。

## 📄 许可证

[MIT](./LICENSE)