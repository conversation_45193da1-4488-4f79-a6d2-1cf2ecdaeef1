# 认证实现文件清单

## 需要创建的文件

### 1. 类型定义文件

**文件**: `src/types/auth.ts`

```typescript
/**
 * 用户认证相关类型定义
 */
export interface AuthUser {
  id: string;
  username: string;
  email?: string;
  name?: string;
  avatar?: string;
  roles?: string[];
}

export interface AuthToken {
  token: string;
  expiresAt?: number;
  refreshToken?: string;
}

export interface AuthCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  token: AuthToken | null;
  isLoading: boolean;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: AuthUser;
  message?: string;
}

export interface AuthError {
  code: string;
  message: string;
  details?: any;
}
```

### 2. 认证工具文件

**文件**: `src/utils/auth.ts`

```typescript
/**
 * 认证工具函数
 * 提供用户认证相关的工具函数，包括token管理、用户信息存储等
 */

import type { AuthUser, AuthToken, AuthCredentials, LoginResponse } from '../types/auth'

// 存储键名常量
const STORAGE_KEYS = {
  TOKEN: 'auth_token',
  USER: 'auth_user'
} as const

export const isAuthenticated = (): boolean => { ... }
export const getToken = (): AuthToken | null => { ... }
export const setToken = (token: AuthToken): void => { ... }
export const getCurrentUser = (): AuthUser | null => { ... }
export const setCurrentUser = (user: AuthUser): void => { ... }
export const clearAuth = (): void => { ... }
export const setAuth = (response: LoginResponse): void => { ... }
export const login = async (credentials: AuthCredentials): Promise<LoginResponse> => { ... }
export const logout = (): void => { ... }
export const getRedirectPath = (): string => { ... }
export const buildLoginUrl = (currentPath: string): string => { ... }
```

### 3. 路由守卫组件

**文件**: `src/components/AuthGuard.tsx`

```typescript
import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { isAuthenticated } from '../utils/auth'

interface AuthGuardProps {
  children: React.ReactNode
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation()

  if (!isAuthenticated()) {
    return <Navigate to={`/login?redirect=${encodeURIComponent(location.pathname + location.search)}`} replace />
  }

  return <>{children}</>
}
```

### 4. 更新路由类型定义

**文件**: `src/router/types.ts` (更新)

```typescript
export interface RouteDefinition {
  path: string;
  component: () => Promise<{ default: React.ComponentType }>;
  name?: string;
  exact?: boolean;
  meta?: {
    title?: string;
    icon?: string;
    requiresAuth?: boolean; // 新增
    public?: boolean; // 新增
    [key: string]: any;
  };
  layout?: LayoutType;
}
```

### 5. 更新登录页面

**文件**: `src/pages/Login.tsx` (更新)

```typescript
import React, { useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { login, setAuth } from '../utils/auth'

const Login: React.FC = () => {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await login({ username, password })
      if (response.success) {
        setAuth(response)
        const redirect = searchParams.get('redirect') || '/'
        navigate(redirect)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <form onSubmit={handleSubmit}>
        <h2>用户登录</h2>
        {error && <div className="error">{error}</div>}
        <div>
          <label>用户名:</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
          />
        </div>
        <div>
          <label>密码:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        <button type="submit" disabled={loading}>
          {loading ? '登录中...' : '登录'}
        </button>
        <p>测试账号: admin/admin 或 user/user</p>
      </form>
    </div>
  )
}

export default Login
```

### 6. 更新路由配置

**文件**: `src/router/routeConfig.ts` (更新)

```typescript
export const baseRoutes: RouteDefinition[] = [
  {
    path: 'login',
    name: 'login',
    component: () => import('../pages/Login'),
    meta: {
      title: '登录',
      icon: '🔑',
      public: true, // 允许公开访问
    },
  },
  {
    path: 'logout',
    name: 'logout',
    component: () => import('../pages/Logout'),
    meta: {
      title: '登出',
      icon: '🚪',
      public: true,
    },
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../pages/NotFound'),
    meta: {
      title: '页面未找到',
      icon: '❌',
      public: true,
    },
  },
];
```

### 7. 更新LazyRouter集成守卫

**文件**: `src/router/LazyRouter.tsx` (更新)
需要在路由渲染部分集成AuthGuard组件

## 集成步骤

1. 按顺序创建上述文件
2. 更新现有文件中的相关配置
3. 在LazyRouter中集成AuthGuard
4. 测试认证流程

## 测试用例

1. 未认证用户访问需要认证的页面 → 重定向到登录
2. 登录成功后 → 重定向到原始页面
3. 已认证用户访问页面 → 正常访问
4. 登出后 → 清除认证信息并重定向
