import js from '@eslint/js'; // 导入 ESLint 核心的推荐规则
import globals from 'globals'; // 导入预设的全局变量
import react from 'eslint-plugin-react'; // 导入 React 插件
import reactHooks from 'eslint-plugin-react-hooks'; // 导入 React Hooks 插件
import reactRefresh from 'eslint-plugin-react-refresh'; // 导入 React Refresh 插件（用于 Vite 等，提供快速刷新警告）
import tseslint from 'typescript-eslint'; // 导入 TypeScript ESLint 插件和解析器
import prettierPlugin from 'eslint-plugin-prettier'; // 导入 Prettier 插件，用于在 ESLint 中运行 Prettier
import prettierConfig from 'eslint-config-prettier'; // 导入 eslint-config-prettier，关闭与 Prettier 冲突的 ESLint 规则

export default tseslint.config(
  // 1. 全局配置：忽略文件
  {
    ignores: ['dist/**', 'node_modules/**', '.idea/**', '.vscode/**'], // 忽略打包输出目录、node_modules 和 IDE/编辑器配置文件夹
  },

  // 2. 基本 JavaScript/TypeScript 和 React 配置
  {
    // 明确指定文件匹配模式，确保规则只应用于 TS/TSX 文件
    files: ['**/*.{ts,tsx}'],
    // 设置语言选项
    languageOptions: {
      ecmaVersion: 2020, // 允许解析 ES2020 语法特性
      sourceType: 'module', // 指定代码为 ES Modules
      globals: {
        ...globals.browser, // 浏览器环境全局变量 (如 window, document)
        // ...globals.node, // 如果是使用 Node.js API 的同构应用或 Next.js 可以在这里添加
        // ... 你也可以在这里添加自定义的全局变量，例如：
        // myGlobalVar: 'readonly',
      },
      // 指定 TypeScript 解析器
      parser: tseslint.parser,
      // 解析器选项
      parserOptions: {
        // 项目的 tsconfig 文件路径，ESLint 会基于这些文件进行类型检查
        project: ['./tsconfig.app.json', './tsconfig.node.json'], // 根据你的实际文件调整
        // 告诉 ESLint 从当前文件目录开始解析 tsconfig 路径
        tsconfigRootDir: import.meta.dirname,
        ecmaFeatures: {
          jsx: true, // 启用 JSX 解析
        },
      },
    },
    // 插件注册
    plugins: {
      '@typescript-eslint': tseslint.plugin, // TypeScript ESLint 插件
      react: react, // React 插件
      'react-hooks': reactHooks, // React Hooks 插件
      'react-refresh': reactRefresh, // React Refresh 插件
      prettier: prettierPlugin, // Prettier 插件
    },
    // 扩展规则集
    extends: [
      js.configs.recommended, // ESLint 核心推荐规则 (基础 JavaScript 规范)
      ...tseslint.configs.recommended, // TypeScript 推荐规则 (类型安全的 TypeScript 实践)
      ...tseslint.configs.recommendedTypeChecked, // 更严格的 TypeScript 类型检查规则
      ...tseslint.configs.stylisticTypeChecked, // TypeScript 风格规则 (如接口命名规范)
      react.configs.recommended, // React 推荐规则（包括 JSX 相关的）
      reactHooks.configs.recommended, // React Hooks 推荐规则
      prettierConfig, // 关闭所有与 Prettier 冲突的 ESLint 格式化规则
      // eslint-plugin-prettier 已经在 plugins 中注册，现在只需要在 rules 中开启对应的规则
    ],
    // 具体规则配置
    rules: {
      // Prettier 规则：将 Prettier 格式化作为 ESLint 错误报告出来
      'prettier/prettier': 'error',

      // React 规则
      'react/react-in-jsx-scope': 'off', // 对于 React 17+ 和新 JSX Transform，不需要手动导入 React
      'react/jsx-uses-react': 'off', // 同上
      'react/prop-types': 'off', // 在 TypeScript 中，Props 类型由接口定义，可禁用此规则
      'react/display-name': 'off', // 可以在匿名函数组件中禁用此规则

      // React Refresh 规则 (通常用于 Vite 或 Webpack HMR 配置)
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true }, // 允许常量导出以进行快速刷新
      ],

      // TypeScript ESLint 规则
      '@typescript-eslint/no-unused-vars': [
        'warn',
        { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }, // 警告未使用的变量，并忽略以下划线开头的变量/参数
      ],
      '@typescript-eslint/no-explicit-any': 'warn', // 允许使用 any，但会发出警告
      '@typescript-eslint/no-non-null-assertion': 'off', // 允许使用非空断言 '!'，根据项目需要开启或关闭
      '@typescript-eslint/consistent-type-imports': [
        'error',
        {
          prefer: 'type-imports', // 强制类型导入使用 'import type'
          fixStyle: 'inline-type-imports', // 自动修复为内联类型导入
        },
      ],
      // @typescript-eslint/explicit-module-boundary-types: 'off', // 根据你是否需要严格的函数返回类型推断决定是否开启
      // @typescript-eslint/ban-ts-comment: 'off', // 允许使用 // @ts-ignore 等注释，根据项目团队规范调整
      // 可以添加更多自定义的 TypeScript 规则
    },
    // 针对特定全局文件的设置（可选，例如针对特定配置文件的特殊规则）
    settings: {
      react: {
        version: 'detect' // 自动检测 React 版本
      }
    }
  },

  // 3. 针对 JavaScript 文件的轻量级配置 (如果项目中存在少量 JS 文件)
  {
    files: ['**/*.js', '**/*.jsx'], // 匹配所有的 JS/JSX 文件
    extends: [
      js.configs.recommended, // 基础 JS 推荐规则
      react.configs.recommended, // React JS 推荐规则
      prettierConfig, // 关闭 Prettier 冲突规则
    ],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: 'module',
      globals: globals.browser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      react: react,
      'react-hooks': reactHooks,
      prettier: prettierPlugin,
    },
    rules: {
      'prettier/prettier': 'error',
      'react/react-in-jsx-scope': 'off',
      'react/jsx-uses-react': 'off',
      'react/prop-types': 'off',
      // 在 JS 文件中禁用 TypeScript 相关的规则
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
    }
  }
);
