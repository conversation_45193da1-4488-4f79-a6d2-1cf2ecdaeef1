// React 和 react-router-dom 的导入，用于构建组件和处理路由
import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
// 导入路由管理器，用于获取和订阅路由变化
import { routeManager } from '../router/RouteManager';
// 导入路由定义类型
import type { RouteDefinition } from '../router/types';

/**
 * DynamicNavigationProps 接口定义了 DynamicNavigation 组件的属性
 *
 * 设计考虑：
 * - className: 允许外部自定义组件的 CSS 类名
 * - style: 允许外部自定义组件的内联样式
 *
 * 扩展性：
 * - 使用可选属性，使组件更灵活
 * - 遵循 React 组件属性设计的最佳实践
 */
interface DynamicNavigationProps {
  /**
   * 可选的 CSS 类名，用于自定义组件样式
   */
  className?: string;

  /**
   * 可选的内联样式对象，用于自定义组件样式
   */
  style?: React.CSSProperties;
}

/**
 * DynamicNavigation 组件用于动态渲染导航菜单
 * 根据路由配置自动生成导航链接，并提供活动状态指示
 *
 * 核心功能：
 * 1. 动态获取路由列表并渲染导航链接
 * 2. 订阅路由变化，实时更新导航菜单
 * 3. 根据当前路径高亮活动链接
 * 4. 过滤不需要显示的路由
 *
 * 设计决策：
 * 1. 使用 useEffect 和 useState 管理组件状态
 * 2. 通过 routeManager.subscribe 实现路由变化订阅
 * 3. 使用 CSS-in-JS 方式定义样式，便于维护和主题化
 * 4. 过滤特定路由（如通配符路由和 404 页面）不显示在导航中
 *
 * 性能优化：
 * - 只在组件挂载时订阅路由变化，避免重复订阅
 * - 在组件卸载时取消订阅，防止内存泄漏
 * - 使用 useMemo 或 useCallback 可进一步优化（当前未使用）
 *
 * 可访问性考虑：
 * - 提供视觉反馈指示当前活动页面
 * - 使用语义化 HTML 结构（nav, ul, li）
 *
 * 扩展性：
 * - 支持通过 props 自定义样式
 * - 可通过 route.meta.icon 添加图标支持
 */
export default function DynamicNavigation({
  className,
  style,
}: DynamicNavigationProps) {
  // 使用 useLocation 获取当前路径，用于确定活动链接
  const location = useLocation();
  // 存储路由列表的状态
  const [routes, setRoutes] = useState<RouteDefinition[]>([]);

  /**
   * useEffect 钩子用于初始化路由列表和订阅路由变化
   * 在组件挂载时执行一次，设置初始路由列表并订阅后续变化
   * 返回清理函数，在组件卸载时取消订阅
   */
  useEffect(() => {
    /**
     * 更新路由列表的函数
     * 从 routeManager 获取最新的路由配置并更新状态
     */
    const updateRoutes = () => {
      setRoutes(routeManager.getRoutes());
    };

    // 初始化路由列表
    updateRoutes();

    /**
     * 订阅路由变化
     * 当路由配置发生变化时，自动更新导航菜单
     * 返回取消订阅的函数，用于组件卸载时清理
     */
    const unsubscribe = routeManager.subscribe(updateRoutes);
    return unsubscribe;
  }, []);

  /**
   * 过滤不需要显示在导航中的路由
   *
   * 过滤规则：
   * 1. 路由必须有名称（route.name 存在）
   * 2. 路由路径不包含通配符 '*'
   * 3. 路由路径不等于 '/404'
   *
   * 设计考虑：
   * - 避免显示无意义的路由（如错误页面、通配符路由）
   * - 确保只有有意义的页面出现在导航中
   */
  const visibleRoutes = routes.filter(
    route => route.name && !route.path.includes('*') && route.path !== '/404'
  );

  /**
   * 导航容器的默认样式
   *
   * 样式特点：
   * - 浅灰色背景，提供视觉区分
   * - 底部边框，增强层次感
   * - 内边距和外边距，确保与其他元素有适当间距
   * - 支持通过 props 传入的自定义样式进行覆盖
   */
  const containerStyle: React.CSSProperties = {
    backgroundColor: '#f8f9fa',
    padding: '1rem',
    marginBottom: '2rem',
    borderBottom: '1px solid #dee2e6',
    ...style,
  };

  /**
   * 导航列表的样式
   *
   * 布局特点：
   * - 使用 flex 布局实现水平排列
   * - 居中对齐，提升视觉效果
   * - 无项目符号，保持简洁外观
   * - 适当的间距，确保链接之间有足够空间
   */
  const listStyle: React.CSSProperties = {
    display: 'flex',
    gap: '2rem',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    justifyContent: 'center',
  };

  /**
   * 导航链接的样式函数
   * 根据当前路径动态调整样式，提供活动状态视觉反馈
   *
   * @param path - 路由路径，用于判断是否为当前活动链接
   * @returns 返回应用于链接的样式对象
   *
   * 视觉反馈机制：
   * - 活动链接使用蓝色文本和加粗字体
   * - 活动链接具有浅蓝色背景
   * - 悬停和过渡效果，提升用户体验
   * - 图标和文本的弹性布局
   */
  const linkStyle = (path: string): React.CSSProperties => ({
    textDecoration: 'none',
    color: location.pathname === path ? '#007bff' : '#495057',
    fontWeight: location.pathname === path ? 'bold' : 'normal',
    padding: '0.5rem 1rem',
    borderRadius: '4px',
    transition: 'all 0.3s ease',
    backgroundColor: location.pathname === path ? '#e7f3ff' : 'transparent',
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
  });

  return (
    /**
     * 导航容器元素
     * 使用语义化的 nav 标签，提升可访问性
     * 支持通过 props 自定义类名和样式
     */
    <nav className={className} style={containerStyle}>
      {/*
       * 导航列表容器
       * 使用无序列表组织导航链接
       */}
      <ul style={listStyle}>
        {/*
         * 遍历可见路由列表，为每个路由生成导航链接
         * 使用 route.path 作为 key，确保列表项的唯一性
         */}
        {visibleRoutes.map(route => (
          <li key={route.path}>
            {/**
             * 使用 react-router-dom 的 Link 组件创建导航链接
             * 根据当前路径动态应用样式
             * 支持通过 route.meta.icon 显示图标
             */}
            <Link to={route.path} style={linkStyle(route.path)}>
              {/**
               * 如果路由配置包含图标，则显示图标
               * 图标通常用于增强导航项的视觉识别
               */}
              {route.meta?.icon && <span>{route.meta.icon}</span>}
              {/**
               * 显示路由名称作为链接文本
               * 路由名称应在路由配置中定义
               */}
              {route.name}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
}
