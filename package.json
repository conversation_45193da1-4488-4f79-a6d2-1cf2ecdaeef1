{"name": "react-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "check": "eslint . --ext .ts,.tsx && tsc --noEmit", "check:fix": "eslint . --ext .ts,.tsx --fix && prettier --write ."}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@azure/msal-browser": "^4.16.0", "@azure/msal-react": "^3.0.16", "ahooks": "^3.9.0", "antd": "^5.26.6", "axios": "^1.11.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}