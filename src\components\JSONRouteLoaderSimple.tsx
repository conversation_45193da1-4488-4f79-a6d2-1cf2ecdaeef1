// React 导入，用于构建组件
import { useState } from 'react';
// 导入动态路由加载相关的函数
import {
  createRoutesFromJSON,
  registerJSONRoutes,
  getAvailableComponents,
} from '../router/dynamicRouteLoader';
// 导入路由定义类型
import type { RouteDefinition } from '../router/types';

/**
 * JSONRouteLoaderSimpleProps 接口定义了 JSONRouteLoaderSimple 组件的属性
 *
 * 设计考虑：
 * - onRoutesLoaded: 可选回调函数，路由加载成功后调用
 * - 支持组件外部监听路由加载事件
 * - 与 JSONRouteLoader 组件保持属性接口一致性
 */
interface JSONRouteLoaderSimpleProps {
  /**
   * 路由加载成功后的回调函数
   * @param routes - 加载的路由定义数组
   */
  onRoutesLoaded?: (routes: RouteDefinition[]) => void;
}

/**
 * JSONRouteLoaderSimple 组件用于从JSON配置动态加载路由（简化版）
 * 提供文本框输入JSON配置，支持验证和注册路由
 * 与 JSONRouteLoader 组件功能相似，但更加轻量级
 *
 * 核心功能：
 * 1. 解析用户输入的JSON路由配置
 * 2. 验证路由配置格式和必需字段
 * 3. 创建并注册路由到路由管理器
 * 4. 显示可用组件列表供用户参考
 * 5. 提供内嵌示例JSON配置加载功能
 * 6. 错误处理和用户反馈
 *
 * 设计决策：
 * 1. 使用 useState 管理组件状态（输入、错误信息）
 * 2. 在组件初始化时获取可用组件列表
 * 3. 提供简化版界面，减少复杂性
 * 4. 内嵌示例数据，避免外部依赖
 * 5. 实现完整的错误处理流程
 * 6. 支持回调函数通知外部组件路由加载结果
 *
 * 与 JSONRouteLoader 的区别：
 * 1. 示例数据内嵌在组件中，而非从外部文件加载
 * 2. 界面更加紧凑，适合嵌入到其他组件中
 * 3. 字体和间距调整以适应更小的空间
 *
 * 用户体验考虑：
 * - 提供清晰的输入提示和占位符
 * - 显示可用组件列表，帮助用户正确配置
 * - 提供示例加载功能，降低使用门槛
 * - 错误信息高亮显示，便于用户识别问题
 * - 禁用空输入时的加载按钮，防止无效操作
 * - 使用较小的字体和按钮，适应紧凑布局
 *
 * 安全性考虑：
 * - 对JSON输入进行解析和验证
 * - 检查必需字段是否存在
 * - 使用try-catch处理解析异常
 *
 * 扩展性：
 * - 通过 onRoutesLoaded 回调支持外部监听
 * - 可轻松添加更多验证规则
 */
export default function JSONRouteLoaderSimple({
  onRoutesLoaded,
}: JSONRouteLoaderSimpleProps) {
  /**
   * 状态变量：JSON输入框的值
   * 存储用户输入的JSON路由配置字符串
   */
  const [jsonInput, setJsonInput] = useState('');

  /**
   * 状态变量：错误信息
   * 存储解析或验证过程中产生的错误信息
   */
  const [error, setError] = useState<string | null>(null);

  /**
   * 状态变量：可用组件列表
   * 从 dynamicRouteLoader 获取支持的组件名称列表
   * 用于提示用户可用的组件选项
   */
  const [availableComponents] = useState(getAvailableComponents());

  /**
   * 处理加载路由的函数
   * 解析JSON输入，验证格式，创建并注册路由
   *
   * 处理流程：
   * 1. 清除之前的错误信息
   * 2. 解析JSON字符串
   * 3. 验证数组格式和必需字段
   * 4. 创建路由定义对象
   * 5. 注册路由到路由管理器
   * 6. 调用回调函数通知外部组件
   * 7. 清空输入框或显示错误信息
   */
  const handleLoadRoutes = () => {
    try {
      // 清除之前的错误信息
      setError(null);
      // 解析JSON输入
      const jsonRoutes = JSON.parse(jsonInput);

      // 验证JSON必须是数组格式
      if (!Array.isArray(jsonRoutes)) {
        throw new Error('JSON 必须是数组格式');
      }

      /**
       * 验证每个路由项的必需字段
       * 确保每个路由都有 path 和 component 字段
       *
       * 验证规则：
       * - path: 路由路径，必需字段
       * - component: 组件名称，必需字段
       */
      jsonRoutes.forEach((route: any, index: number) => {
        if (!route.path || !route.component) {
          throw new Error(`路由 ${index} 缺少必需的 path 或 component 字段`);
        }
      });

      // 从JSON创建路由定义对象
      const routes = createRoutesFromJSON(jsonRoutes);

      /**
       * 注册路由到路由管理器
       * 使新路由在应用中生效
       */
      registerJSONRoutes(jsonRoutes);

      /**
       * 如果提供了回调函数，则调用通知外部组件
       * 传递创建的路由定义数组
       */
      if (onRoutesLoaded) {
        onRoutesLoaded(routes);
      }

      // 清空输入框
      setJsonInput('');
    } catch (err) {
      /**
       * 处理解析或验证错误
       * 提取错误信息并显示给用户
       */
      setError(err instanceof Error ? err.message : 'JSON 解析失败');
    }
  };

  /**
   * 加载内嵌示例JSON配置的函数
   * 提供预定义的示例路由配置
   * 用于演示和帮助用户理解JSON格式
   *
   * 设计考虑：
   * - 内嵌示例数据，避免外部文件依赖
   * - 提供典型的路由配置示例
   * - 包含不同复杂度的路由（带meta数据和不带meta数据）
   */
  const loadExample = () => {
    /**
     * 内嵌的示例路由配置数组
     * 包含两个示例路由：
     * 1. 仪表盘路由：包含完整配置和元数据
     * 2. 个人资料路由：包含基本配置
     */
    const exampleRoutes = [
      {
        path: '/dashboard',
        component: 'Home',
        name: '仪表盘',
        meta: { title: '用户仪表盘', icon: '📊' },
      },
      {
        path: '/profile',
        component: 'About',
        name: '个人资料',
      },
    ];
    /**
     * 将示例路由配置转换为格式化的JSON字符串
     * 使用2个空格缩进，便于阅读
     * 设置到输入框状态
     */
    setJsonInput(JSON.stringify(exampleRoutes, null, 2));
  };

  /**
   * 渲染简化版JSON路由加载器界面
   * 包含输入框、按钮和错误显示区域
   * 界面更加紧凑，适合嵌入到其他组件中
   */
  return (
    /**
     * 组件主容器
     * 使用边框和背景色区分功能区域
     * 圆角和内边距提升视觉效果
     * 与 JSONRouteLoader 保持一致的样式设计
     */
    <div
      style={{
        border: '1px solid #ccc',
        borderRadius: '8px',
        padding: '20px',
        margin: '20px 0',
        backgroundColor: '#f8f9fa',
      }}
    >
      <h4>JSON 路由加载器</h4>
      <div style={{ marginBottom: '15px' }}>
        <p style={{ fontSize: '12px', color: '#666' }}>
          可用组件: {availableComponents.join(', ')}
        </p>
      </div>
      <textarea
        value={jsonInput}
        onChange={e => setJsonInput(e.target.value)}
        placeholder='[{"path": "/example", "component": "Home", "name": "示例页面"}]'
        style={{
          width: '100%',
          minHeight: '150px',
          padding: '10px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          fontFamily: 'monospace',
          fontSize: '12px',
        }}
      />
      {error && (
        <div
          style={{
            color: '#dc3545',
            margin: '10px 0',
            padding: '10px',
            backgroundColor: '#f8d7da',
            borderRadius: '4px',
            fontSize: '12px',
          }}
        >
          {error}
        </div>
      )}
      <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
        <button
          onClick={handleLoadRoutes}
          disabled={!jsonInput.trim()}
          style={{
            padding: '6px 12px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          加载路由
        </button>
        <button
          onClick={loadExample}
          style={{
            padding: '6px 12px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          加载示例
        </button>
      </div>
    </div>
  );
}
