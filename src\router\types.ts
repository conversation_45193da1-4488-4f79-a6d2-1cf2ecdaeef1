import type { ComponentType } from 'react';

/**
 * 布局类型常量
 * 定义了支持的布局类型
 */
export const LayoutType = {
  DEFAULT: 'default',
  BLANK: 'blank',
} as const;

/**
 * 布局类型
 */
export type LayoutType = (typeof LayoutType)[keyof typeof LayoutType];

/**
 * 路由定义接口
 * 描述一个路由的基本信息和配置
 */
export interface RouteDefinition {
  /**
   * 路由路径
   */
  path: string;

  /**
   * 组件懒加载函数，返回一个包含默认导出组件的 Promise
   */
  component: () => Promise<{ default: ComponentType }>;

  /**
   * 路由名称（可选）
   */
  name?: string;

  /**
   * 是否精确匹配（可选，默认为 false）
   */
  exact?: boolean;

  /**
   * 路由元信息（可选），可以包含标题、图标等自定义数据
   */
  meta?: {
    title?: string;
    icon?: string;
    requiresAuth?: boolean; // 新增：是否需要认证
    public?: boolean; // 新增：是否允许公开访问
    [key: string]: any;
  };

  /**
   * 布局类型（可选，默认为 default）
   * 用于指定路由使用的布局
   */
  layout?: LayoutType;
}

/**
 * 已加载路由接口
 * 描述一个已经加载的路由信息
 */
export interface LoadedRoute {
  /**
   * 路由路径
   */
  path: string;

  /**
   * 已加载的组件
   */
  component: ComponentType;

  /**
   * 路由名称（可选）
   */
  name?: string;

  /**
   * 是否精确匹配（可选）
   */
  exact?: boolean;

  /**
   * 路由元信息（可选）
   */
  meta?: Record<string, any>;

  /**
   * 布局类型
   */
  layout?: LayoutType;
}

/**
 * 路由缓存接口
 * 以路径为键，已加载路由为值的映射
 */
export interface RouteCache {
  /**
   * 路径到已加载路由的映射
   */
  [path: string]: LoadedRoute;
}

/**
 * 路由器配置接口
 * 定义路由器的各种配置选项
 */
export interface RouterConfig {
  /**
   * 加载组件（可选），在组件加载时显示
   */
  loadingComponent?: ComponentType;

  /**
   * 错误组件（可选），在组件加载失败时显示
   */
  errorComponent?: ComponentType;

  /**
   * 是否启用缓存（可选，默认为 true）
   */
  enableCache?: boolean;

  /**
   * 最大缓存大小（可选，默认为 50）
   */
  maxCacheSize?: number;
}

/**
 * 布局配置接口
 * 定义不同布局的配置
 */
export interface LayoutConfig {
  /**
   * 布局类型
   */
  type: LayoutType;

  /**
   * 布局组件
   */
  component: ComponentType;

  /**
   * 布局名称
   */
  name: string;
}

/**
 * 路由注册器类型
 * 定义了注册路由的函数签名
 */
export type RouteRegistrar = (route: RouteDefinition) => void;

/**
 * 路由注销器类型
 * 定义了注销路由的函数签名
 */
export type RouteUnregistrar = (path: string) => void;

export interface JSONRouteConfig {
  path: string;
  component: string;
  name?: string;
  exact?: boolean;
  layout?: LayoutType;
  meta?: Record<string, any>;
}
