# React 模板项目文档

## 项目概述

这是一个基于 React + TypeScript + Vite 的现代化前端开发模板，集成了完整的路由系统、认证机制和最佳实践。该模板旨在为开发者提供一个功能齐全、易于扩展的起点，用于构建现代化的单页应用(SPA)。

## 核心特性

### 🚀 现代化技术栈
- **React 19** - 最新的 React 版本，支持所有新特性
- **TypeScript** - 强类型检查，提高代码质量和开发体验
- **Vite 7** - 极速的构建工具，提供闪电般的开发体验
- **React Router v7** - 客户端路由管理

### 🛣️ 高级路由系统
- **动态路由加载** - 支持运行时添加/删除路由
- **组件懒加载** - 按需加载页面组件，优化性能
- **路由缓存** - LRU缓存策略，提高重复访问速度
- **JSON路由配置** - 通过JSON配置动态注册路由
- **布局系统** - 支持多种页面布局

### 🔐 认证与权限
- **路由守卫** - 全局认证检查，保护私有路由
- **MSAL 集成** - 支持 Microsoft 身份验证
- **Token 管理** - 自动处理认证令牌存储和刷新

### 🎨 UI 组件库
- **Ant Design** - 丰富的UI组件库
- **响应式设计** - 适配各种屏幕尺寸
- **可复用组件** - Loading、错误边界等通用组件

### 📦 状态管理与数据获取
- **TanStack Query** - 服务器状态管理，支持缓存、同步等
- **SWR** - 另一种数据获取方案
- **Zustand** - 客户端状态管理

## 项目结构

```
src/
├── components/           # 通用组件
│   ├── AuthGuard.tsx     # 路由守卫组件
│   ├── DynamicNavigation.tsx # 动态导航组件
│   ├── ErrorBoundary.tsx # 错误边界组件
│   ├── JSONRouteLoader.tsx # JSON路由加载器
│   ├── LoadingSpinner.tsx # 加载指示器
│   └── RouteDebugger.tsx # 路由调试工具
├── layouts/              # 页面布局组件
│   └── DefaultLayout.tsx # 默认布局
├── pages/                # 页面组件
│   ├── About.tsx         # 关于页面
│   ├── Contact.tsx       # 联系页面
│   ├── Dashboard.tsx     # 仪表盘页面
│   ├── Home.tsx          # 首页
│   ├── Login.tsx         # 登录页面
│   ├── Menu.tsx          # 菜单页面
│   └── NotFound.tsx      # 404页面
├── router/               # 路由系统
│   ├── LazyRoute.tsx     # 懒加载路由组件
│   ├── LazyRouter.tsx    # 主路由组件
│   ├── RouteCache.ts     # 路由缓存管理
│   ├── RouteManager.ts   # 路由管理器
│   ├── dynamicRouteLoader.ts # 动态路由加载器
│   ├── routeConfig.ts    # 基础路由配置
│   └── types.ts          # 路由类型定义
├── types/                # 全局类型定义
│   └── auth.ts           # 认证相关类型
├── utils/                # 工具函数
│   ├── auth.ts           # 认证工具函数
│   └── request.ts        # HTTP请求工具
├── App.css               # 全局样式
├── App.tsx               # 应用根组件
└── main.tsx              # 应用入口点
```

## 快速开始

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发模式

```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 预览生产构建

```bash
npm run preview
# 或
yarn preview
```

## 路由系统详解

### 基础路由配置

项目使用基于组件的路由配置，通过 [routeConfig.ts](file:///d:\WorkArea\react\react-template\src\router\routeConfig.ts) 文件定义基础路由。

### 动态路由加载

支持在运行时通过JSON配置动态加载路由：

```typescript
import { registerJSONRoutes } from './router/dynamicRouteLoader';

const jsonRoutes = [
  {
    path: '/dynamic-page',
    component: 'Home', // 对应 pages/Home.tsx
    name: '动态页面',
    meta: { title: '动态加载的页面' }
  }
];

registerJSONRoutes(jsonRoutes);
```

### 路由缓存

路由系统内置LRU缓存机制，可通过配置调整缓存大小：

```typescript
<LazyRouter
  config={{
    loadingComponent: LoadingSpinner,
    enableCache: true,
    maxCacheSize: 50
  }}
/>
```

## 认证系统

### MSAL 集成

项目集成了 Microsoft 身份验证库(MSAL)，支持 Azure AD 认证。

### 路由守卫

通过 [AuthGuard](file:///d:\WorkArea\react\react-template\src\components\AuthGuard.tsx) 组件实现路由级别的访问控制：

```typescript
<Route 
  path="/protected" 
  element={
    <AuthGuard>
      <ProtectedComponent />
    </AuthGuard>
  } 
/>
```

## 数据获取

### TanStack Query

项目使用 TanStack Query 管理服务器状态：

```typescript
import { useQuery } from '@tanstack/react-query';

function UserProfile() {
  const { data, isLoading } = useQuery({
    queryKey: ['user'],
    queryFn: fetchUser
  });
  
  // 组件逻辑
}
```

### SWR

也集成了 SWR 作为备选的数据获取方案：

```typescript
import useSWR from 'swr';

function UserProfile() {
  const { data, error } = useSWR('/api/user', fetcher);
  
  // 组件逻辑
}
```

## 开发规范

### 代码风格

- 使用 ESLint 和 Prettier 保证代码风格一致性
- 遵循 TypeScript 最佳实践
- 组件使用函数式写法和 Hooks

### 提交规范

- 使用 conventional commits 规范提交信息
- 提交前运行 lint 和测试

## 部署

### 构建

```bash
npm run build
```

构建产物位于 `dist` 目录，可部署到任何静态文件服务器。

### 环境配置

通过 `.env` 文件配置环境变量：

```env
VITE_API_URL=https://api.example.com
VITE_APP_TITLE=My App
```

## 故障排除

### 常见问题

1. **路由未生效** - 确认是否正确调用路由注册方法
2. **组件未找到** - 检查组件名称是否与文件名匹配
3. **认证失败** - 检查 MSAL 配置和网络连接

### 调试工具

使用内置的 [RouteDebugger](file:///d:\WorkArea\react\react-template\src\components\RouteDebugger.tsx) 组件查看当前路由状态：

```typescript
import RouteDebugger from './components/RouteDebugger';

function App() {
  return (
    <>
      {/* 其他组件 */}
      <RouteDebugger />
    </>
  );
}
```